# YouTube Shorts 批量上传自动化工具

基于比特浏览器 + Playwright 的 YouTube Shorts 批量上传自动化工具，支持多账号并发操作，从 Excel 读取任务清单，自动完成视频上传、信息填写和发布流程。

## 功能特性

- ✅ **多账号管理**: 基于比特浏览器 profile 实现多账号隔离
- ✅ **批量上传**: 从 Excel 读取任务清单，批量处理视频上传
- ✅ **完整配置**: 支持标题、描述、标签、可见性、定时发布等完整设置
- ✅ **并发执行**: 支持多个 profile 并发操作，提高效率
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **详细日志**: 每步操作的详细日志记录和截图保存
- ✅ **结果记录**: 自动生成执行结果报告

## 环境要求

- Windows 操作系统
- Python 3.8+
- 比特浏览器已安装并运行
- 各 profile 已完成 YouTube 登录

## 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd YouTubeBatchPublishClaude
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv .venv
   .venv\Scripts\activate
   ```

3. **安装依赖**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

4. **安装 Playwright 浏览器（如需要）**
   ```bash
   playwright install
   ```

## 使用方法

### 1. 创建示例文件

```bash
# 创建示例 Excel 任务文件
python -m youtube_uploader --create-sample

# 创建默认配置文件
python -m youtube_uploader --create-config
```

### 2. 准备任务文件

编辑 `sample_tasks.xlsx` 文件，填入你的任务信息：

| 字段 | 说明 | 示例 |
|------|------|------|
| profile_name | 比特浏览器 profile 名称 | profile1 |
| video_path | 视频文件绝对路径 | C:/videos/sample.mp4 |
| title | 视频标题 | 我的视频标题 |
| description | 视频描述 | 这是视频描述 |
| tags | 标签（用\|分隔） | 教程\|Python\|自动化 |
| visibility | 可见性 | public/unlisted/private/scheduled |
| ... | 其他字段 | 参考示例文件 |

### 3. 运行程序

```bash
# 基本运行
python -m youtube_uploader --excel tasks.xlsx

# 指定并发数
python -m youtube_uploader --excel tasks.xlsx --concurrency 2

# 使用自定义配置
python -m youtube_uploader --excel tasks.xlsx --config config.yml

# 仅处理特定 profile
python -m youtube_uploader --excel tasks.xlsx --profiles "profile1,profile2"

# 仅验证任务文件
python -m youtube_uploader --excel tasks.xlsx --validate-only
```

## 配置说明

配置文件 `config.yml` 示例：

```yaml
# 并发设置
concurrency: 1

# 超时设置（秒）
timeouts:
  page_load: 30
  element_wait: 10
  upload_process: 300
  video_processing: 600

# 重试设置
retry:
  max_attempts: 3
  delay_seconds: 3

# 操作设置
operation:
  interval_seconds: 10
  random_delay_range: [2, 5]

# 文件设置
files:
  excel_sheet_name: "Tasks"
  result_file: "upload_results.xlsx"
  log_dir: "logs"
  screenshot_dir: "screenshots"

# 调试设置
debug:
  save_screenshots: true
  save_page_html: false
  verbose_logging: true
```

## 环境变量覆盖

支持通过环境变量覆盖配置：

```bash
set YOUTUBE_UPLOADER_CONCURRENCY=2
set YOUTUBE_UPLOADER_OPERATION_INTERVAL_SECONDS=15
python -m youtube_uploader --excel tasks.xlsx
```

## 输出文件

- **日志文件**: `logs/youtube_uploader_YYYYMMDD_HHMMSS.log`
- **结果文件**: `results_YYYYMMDD_HHMMSS.xlsx`
- **错误截图**: `screenshots/error_*.png`

## 注意事项

1. **账号安全**: 确保各 profile 已正确登录 YouTube
2. **文件路径**: 使用绝对路径指定视频文件
3. **网络稳定**: 确保网络连接稳定，避免上传中断
4. **操作间隔**: 建议保持适当的操作间隔，避免触发风控
5. **人工验证**: 遇到验证码等情况需要人工处理

## 故障排除

### 常见问题

1. **比特浏览器连接失败**
   - 检查比特浏览器是否正常运行
   - 确认端口 54345 可用

2. **Profile 不存在**
   - 检查 profile 名称是否正确
   - 确认 profile 已在比特浏览器中创建

3. **视频上传失败**
   - 检查视频文件是否存在且为 MP4 格式
   - 确认网络连接稳定
   - 查看详细日志和错误截图

4. **元素定位失败**
   - YouTube 界面可能已更新
   - 需要更新选择器映射

## 开发说明

项目结构：
```
youtube_uploader/
├── __init__.py          # 包初始化
├── models.py            # 数据模型
├── config.py            # 配置管理
├── bit_browser.py       # 比特浏览器集成
├── excel_handler.py     # Excel 处理
├── youtube_automation.py # YouTube 自动化
├── executor.py          # 任务执行引擎
├── runner.py            # 主程序入口
├── utils.py             # 工具函数
└── selectors.py         # 界面选择器
```

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和平台服务条款。
