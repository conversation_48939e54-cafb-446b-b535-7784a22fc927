# 🎯 手动获取YouTube选择器指南

如果自动录制有问题，您可以手动获取准确的选择器：

## 📋 操作步骤

### 1. 打开比特浏览器
- 运行您的比特浏览器profile
- 导航到 https://www.youtube.com/upload

### 2. 打开开发者工具
- 按 `F12` 或右键选择"检查"
- 点击 `Console` 标签

### 3. 复制粘贴这段代码到控制台

```javascript
// YouTube选择器获取工具
window.getYouTubeSelectors = function() {
    const selectors = {};
    
    // 文件上传输入
    const fileInput = document.querySelector('input[type="file"]');
    if (fileInput) {
        selectors.fileInput = getUniqueSelector(fileInput);
        console.log('📁 文件上传:', selectors.fileInput);
    }
    
    // 标题输入框
    const titleInputs = document.querySelectorAll('div[contenteditable="true"], textarea, input[type="text"]');
    titleInputs.forEach((input, index) => {
        const selector = getUniqueSelector(input);
        const placeholder = input.placeholder || input.getAttribute('aria-label') || '';
        console.log(`📝 输入框 ${index + 1}:`, selector, '- 提示:', placeholder);
        
        if (placeholder.toLowerCase().includes('title') || placeholder.includes('标题')) {
            selectors.titleInput = selector;
        } else if (placeholder.toLowerCase().includes('description') || placeholder.includes('描述')) {
            selectors.descriptionInput = selector;
        }
    });
    
    // 发布按钮
    const publishButtons = document.querySelectorAll('button');
    publishButtons.forEach((btn, index) => {
        const text = btn.textContent?.trim() || '';
        const ariaLabel = btn.getAttribute('aria-label') || '';
        
        if (text.toLowerCase().includes('publish') || text.includes('发布') || 
            ariaLabel.toLowerCase().includes('publish') || ariaLabel.includes('发布')) {
            const selector = getUniqueSelector(btn);
            selectors.publishButton = selector;
            console.log('🚀 发布按钮:', selector, '- 文本:', text);
        }
    });
    
    // 进度条
    const progressBars = document.querySelectorAll('[role="progressbar"]');
    if (progressBars.length > 0) {
        selectors.progressBar = getUniqueSelector(progressBars[0]);
        console.log('⏳ 进度条:', selectors.progressBar);
    }
    
    // 可见性设置
    const visibilityButtons = document.querySelectorAll('[aria-label*="visibility" i], [aria-label*="可见性" i]');
    if (visibilityButtons.length > 0) {
        selectors.visibilityButton = getUniqueSelector(visibilityButtons[0]);
        console.log('👁️ 可见性设置:', selectors.visibilityButton);
    }
    
    return selectors;
};

function getUniqueSelector(element) {
    // 优先使用aria-label
    const ariaLabel = element.getAttribute('aria-label');
    if (ariaLabel) {
        return `[aria-label="${ariaLabel}"]`;
    }
    
    // 使用ID
    if (element.id) {
        return '#' + element.id;
    }
    
    // 使用data-testid
    const testId = element.getAttribute('data-testid');
    if (testId) {
        return `[data-testid="${testId}"]`;
    }
    
    // 使用role + 其他属性
    const role = element.getAttribute('role');
    if (role) {
        let selector = `[role="${role}"]`;
        const type = element.getAttribute('type');
        if (type) selector += `[type="${type}"]`;
        return selector;
    }
    
    // 构建CSS路径
    let path = [];
    let current = element;
    
    while (current && current.tagName && path.length < 4) {
        let selector = current.tagName.toLowerCase();
        
        // 添加重要属性
        if (current.type) selector += `[type="${current.type}"]`;
        if (current.className) {
            const classes = current.className.split(' ')
                .filter(c => c && c.length > 2 && !c.match(/^[a-z0-9]{8,}$/i))
                .slice(0, 2);
            if (classes.length > 0) {
                selector += '.' + classes.join('.');
            }
        }
        
        path.unshift(selector);
        current = current.parentElement;
    }
    
    return path.join(' > ');
}

console.log('🎯 YouTube选择器工具已加载！');
console.log('📋 使用方法:');
console.log('1. 执行: window.getYouTubeSelectors()');
console.log('2. 复制输出的选择器');
console.log('3. 发送给开发者');
```

### 4. 执行获取命令
在控制台输入并执行：
```javascript
window.getYouTubeSelectors()
```

### 5. 复制输出结果
- 控制台会显示所有找到的选择器
- 复制所有输出内容
- 发送给我进行代码生成

## 🎯 关键元素说明

需要获取的关键选择器：

1. **文件上传输入** - `input[type="file"]`
2. **标题输入框** - 通常是 `div[contenteditable="true"]`
3. **描述输入框** - 可能是 `textarea` 或 `div[contenteditable="true"]`
4. **发布按钮** - 包含"发布"或"Publish"文本的按钮
5. **进度条** - `[role="progressbar"]`
6. **可见性设置** - 隐私设置按钮

## 📱 操作流程

1. 在比特浏览器中打开YouTube上传页面
2. 按F12打开开发者工具
3. 复制粘贴上面的JavaScript代码
4. 执行 `window.getYouTubeSelectors()`
5. 复制所有输出结果发给我

这样我就能获得准确的选择器，不用再猜测了！
