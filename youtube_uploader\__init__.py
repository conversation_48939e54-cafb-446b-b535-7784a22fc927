"""
YouTube Shorts 批量上传自动化工具

基于比特浏览器 + Playwright 的 YouTube Shorts 批量上传自动化工具，
支持多账号并发操作，从 Excel 读取任务清单，自动完成视频上传、信息填写和发布流程。
"""

__version__ = "1.0.0"
__author__ = "YouTube Uploader Team"
__description__ = "YouTube Shorts 批量上传自动化工具"

# 导出主要类和函数
try:
    from .models import TaskModel, ConfigModel, ResultModel
    from .config import Config
    from .runner import YouTubeUploaderRunner

    __all__ = [
        "TaskModel",
        "ConfigModel",
        "ResultModel",
        "Config",
        "YouTubeUploaderRunner",
    ]
except ImportError:
    # 在开发阶段可能会有导入问题，这里先忽略
    __all__ = []
