#!/usr/bin/env python3
"""
YouTube批量上传运行脚本
使用修复后的contenteditable和选择器
"""

import sys
import asyncio
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

async def run_youtube_upload():
    """运行YouTube上传"""
    try:
        print("🚀 启动YouTube批量上传...")
        print("=" * 70)
        
        # 1. 读取任务数据
        print("\n1️⃣ 读取任务数据...")
        from youtube_uploader.excel_handler import ExcelHandler
        
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        
        if not tasks:
            print("❌ 未找到任务数据")
            return False
        
        print(f"✅ 读取到 {len(tasks)} 个任务")
        for i, task in enumerate(tasks):
            print(f"   任务 {i+1}: {task.title} ({task.profile_name})")
        
        # 2. 初始化执行器
        print(f"\n2️⃣ 初始化执行器...")
        from youtube_uploader.executor import TaskExecutor

        # 配置
        config = {
            'max_retries': 3,
            'retry_delay': 5,
            'page_load_timeout': 30,
            'element_timeout': 15,
            'upload_timeout': 600,
            'screenshot_dir': 'screenshots'
        }

        executor = TaskExecutor(config)
        
        # 3. 开始执行任务
        print(f"\n3️⃣ 开始批量上传...")
        print("=" * 70)

        results = await executor.execute_tasks(tasks)
        
        # 4. 显示结果
        print("\n" + "=" * 70)
        print("📊 上传结果统计:")
        print("=" * 70)
        
        success_count = 0
        failed_count = 0
        
        for i, result in enumerate(results):
            print(f"\n任务 {i+1}: {result.profile_name}")
            print(f"  状态: {result.get_summary()}")
            
            if result.is_success():
                success_count += 1
                if result.video_url:
                    print(f"  视频链接: {result.video_url}")
            else:
                failed_count += 1
                if result.error_message:
                    print(f"  错误: {result.error_message}")
        
        print(f"\n📈 总计:")
        print(f"  ✅ 成功: {success_count}")
        print(f"  ❌ 失败: {failed_count}")
        print(f"  📊 成功率: {success_count/(success_count+failed_count)*100:.1f}%")
        
        # 5. 保存结果
        print(f"\n5️⃣ 保存结果...")
        try:
            excel_handler.write_results(results, 'upload_results.xlsx')
            print(f"✅ 结果已保存到: upload_results.xlsx")
        except Exception as e:
            print(f"⚠️ 保存结果失败: {e}")
        
        print("\n" + "=" * 70)
        print("🎉 YouTube批量上传完成!")
        print("=" * 70)
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 上传失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("YouTube批量上传工具")
    print("基于修复后的contenteditable和选择器")
    print()
    
    # 检查必要文件
    if not Path('tasks_template.xlsx').exists():
        print("❌ 未找到 tasks_template.xlsx 文件")
        print("请确保Excel文件存在并包含任务数据")
        sys.exit(1)
    
    # 运行上传
    result = asyncio.run(run_youtube_upload())
    
    if result:
        print("\n🎉 上传成功完成!")
    else:
        print("\n❌ 上传过程中出现问题")
        print("请检查日志和截图文件夹")
    
    input("\n按回车键退出...")
