# YouTube Shorts 批量上传自动化（基于比特浏览器 + Playwright）

## 1. 概述
- 目的：从 Excel（.xlsx）读取“视频与发布信息⇄比特浏览器 profile 名”的映射，自动在对应 profile 的 YouTube Studio 中完成 Shorts 视频上传与发布。
- 面向对象：多账号运营（每账号一个比特浏览器 profile，已登录）。
- 输出：每条任务的执行结果（成功/失败、视频链接/ID、错误信息），日志与关键步骤截图。

## 2. 范围与非范围
- 范围
  - 支持 Shorts 视频上传
  - 填写标题、描述、标签
  - 设置：是否为儿童、是否 18+、播放列表、是否允许混剪（Remix）、语言、可见性（含定时）
  - YPP 广告适合度：如页面出现，自动作出“以上都没有”的选择并提交
  - 支持串行/并发（并发数可配置）
  - 任务结果记录到结果表（CSV/Excel），保存日志与截图
- 非范围（首版）
  - 自动突破手机号验证、二次验证、CAPTCHA 等（标记人工处理）
  - 使用外部 API 直接上传（走 UI 自动化为主）

## 3. 前提与依赖
- 前提
  - 各账号已在对应比特浏览器 profile 中完成登录，正常可直接进入 Studio
  - 本地比特浏览器服务可用，并能通过其 API 打开窗口，获得 WebSocket 调试地址
- 技术栈
  - Python 3.x
  - Playwright（通过 CDP 连接已启动的比特浏览器实例）
  - openpyxl（Excel 读写）
  - pydantic（数据校验）
  - tenacity（重试）
  - logging/loguru（日志）
- 依赖安装：基于 venv（不使用全局 Python 环境）

## 4. 数据模型（Excel 任务清单）
- 工作表名：Tasks（可在配置中覆盖）
- 每一行代表一个上传任务，列（建议表头）如下：
  - profile_name：对应比特浏览器的 profile 名（逐任务配置，取代固定前缀策略）
  - video_path：视频文件绝对路径
  - title：标题
  - description：描述（可多行）
  - tags：标签（使用 | 分隔，如“教程|Python|自动化”）
  - visibility：public | unlisted | private | scheduled
  - schedule_time：可选，visibility=scheduled 时使用；格式示例“2025-08-20 14:30:00”（本地时区）
  - audience_made_for_kids：true | false（是否为儿童）
  - age_restriction_18plus：true | false（是否 18+）
  - playlist：可选，存在则选择（不存在时本版默认不自动创建，可后续迭代）
  - allow_remix：true | false（是否允许混剪/Remix）
  - language：如“zh-Hans”“zh-CN”“en-US”等
  - thumbnail_path：可选（Shorts 一般不需；保留以兼容）
  - note：可选备注
- 说明：YPP 广告适合度字段不需要在 Excel 中体现；若该页面出现，系统自动选择“以上都没有”并提交。

## 5. 自动化流程（单任务）
1) 查找/创建 profile：根据 profile_name 获取或创建对应比特浏览器实例
2) 打开窗口并获取 ws：调用比特浏览器 API 打开窗口，拿到 WebSocket 调试地址
3) 连接 Playwright：chromium.connect_over_cdp(ws) 连接到已有实例（复用 profile 登录态与代理）
4) 打开上传页：进入 YouTube Studio（https://studio.youtube.com 或 https://www.youtube.com/upload）
5) 上传文件：定位 input[type="file"]，set_input_files(video_path)，等待上传/处理进度
6) 填写信息：标题、描述、标签（必要时点击“显示更多”）
7) 设置参数：
   - 受众（是否为儿童）
   - 年龄限制（18+）
   - 播放列表（若存在）
   - 允许混剪（Remix）
   - 语言（原始语言）
   - 缩略图（若提供）
8) YPP 广告适合度（如出现）：选择“以上都没有”，直接提交
9) 可见性：public/unlisted/private/scheduled；若 scheduled 填写日期时间
10) 发布：点击发布/完成，等待确认；抓取视频链接/ID
11) 记录结果：写入结果表，保存日志与截图

## 6. 并发与稳定性
- 并发：默认串行；可配置 concurrency>1 以并发执行（建议 1–3 起步）
- 等待与选择器：显式等待（可见/可点击/文本出现）、尽量使用稳定文本/ARIA 定位
- 随机延时：在关键步骤间加入小幅随机延时，降低风控风险
- 重试：关键操作可有限次重试（如 1–2 次）
- 超时：对页面加载、元素等待、上传处理设置合理超时（可配置）

## 7. 失败与人工介入
- 遇到账号验证/手机号/可疑登录/CAPTCHA：标记“needs_verification”，记录日志与截图，跳过该任务
- 元素变化导致的定位失败：记录并截图，后续更新选择器映射

## 8. 配置项（config.yml 示例）
- concurrency：并发度（默认 1）
- timeouts：元素等待、页面加载、上传处理超时
- retry：次数与间隔
- artifacts：是否保存截图/HTML 片段
- excel：path、sheet、列名映射（支持自定义列名）

## 9. 命令行与产出
- 运行命令（示例）：
  - python -m youtube_uploader.runner --excel tasks.xlsx --concurrency 2
- 产出：
  - 结果表（CSV/Excel）：记录 success/failed、video_url/video_id、error_message、finished_at
  - logs/：运行日志
  - artifacts/：截图、HTML 片段

## 10. 环境与依赖（venv）
- Windows（示例）
  - python -m venv .venv
  - .venv\Scripts\activate
  - pip install --upgrade pip
  - pip install playwright openpyxl pydantic tenacity loguru
  - 仅当需要时执行：playwright install（通常连接现有浏览器无需）

## 11. 验收标准
- PoC：单任务可在指定 profile 中成功上传并发布 Shorts，填写标题/描述/标签/受众，设置“未列出”，输出视频链接/ID
- V1：
  - 覆盖 Excel 全字段（含定时、18+、播放列表、混剪、语言、缩略图）
  - 并发可配置，并具备重试与日志/截图
  - 如出现 YPP 分级页面，可自动选择“以上都没有”并提交

## 12. 风险与合规
- 风控：控制并发与操作节奏，保持指纹/网络一致性
- 合规：遵守 YouTube/Google 服务条款与相关法律法规；需人工处理的验证流程不做自动绕过

