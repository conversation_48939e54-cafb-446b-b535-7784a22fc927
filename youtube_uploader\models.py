"""
数据模型定义

使用 Pydantic 定义任务数据模型、配置模型和结果模型，包括数据验证规则。
"""

from datetime import datetime
from typing import Optional, List, Union, Dict, Any
from enum import Enum
from pathlib import Path
import re
from pydantic import BaseModel, Field, field_validator, model_validator


class VisibilityType(str, Enum):
    """视频可见性类型"""
    PUBLIC = "public"
    UNLISTED = "unlisted" 
    PRIVATE = "private"
    SCHEDULED = "scheduled"


class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    NEEDS_VERIFICATION = "needs_verification"


class LanguageCode(str, Enum):
    """支持的语言代码"""
    ZH_CN = "zh-CN"  # 简体中文
    ZH_TW = "zh-TW"  # 繁体中文
    EN_US = "en-US"  # 英语（美国）
    EN_GB = "en-GB"  # 英语（英国）
    JA_JP = "ja-JP"  # 日语
    KO_KR = "ko-KR"  # 韩语
    ES_ES = "es-ES"  # 西班牙语
    FR_FR = "fr-FR"  # 法语
    DE_DE = "de-DE"  # 德语
    IT_IT = "it-IT"  # 意大利语
    PT_BR = "pt-BR"  # 葡萄牙语（巴西）
    RU_RU = "ru-RU"  # 俄语


# 常量定义
SUPPORTED_VIDEO_FORMATS = ['.mp4', '.mov', '.avi', '.mkv', '.webm']
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
MAX_TITLE_LENGTH = 100
MAX_DESCRIPTION_LENGTH = 5000
MAX_TAGS_COUNT = 15
MAX_TAG_LENGTH = 30


class TaskModel(BaseModel):
    """任务数据模型"""

    # 必填字段
    profile_name: str = Field(..., min_length=1, max_length=50, description="比特浏览器 profile 名称")
    video_path: str = Field(..., description="视频文件绝对路径")
    title: str = Field(..., min_length=1, max_length=MAX_TITLE_LENGTH, description="标题")
    visibility: VisibilityType = Field(..., description="可见性设置")
    audience_made_for_kids: bool = Field(..., description="是否为儿童内容")
    age_restriction_18plus: bool = Field(..., description="是否18+限制")
    allow_remix: bool = Field(..., description="是否允许混剪")
    language: str = Field(..., description="语言代码")

    # 可选字段
    description: Optional[str] = Field(None, max_length=MAX_DESCRIPTION_LENGTH, description="描述")
    tags: Optional[str] = Field(None, description="标签（用|分隔）")
    schedule_time: Optional[str] = Field(None, description="定时发布时间")
    playlist: Optional[str] = Field(None, max_length=100, description="播放列表名称")
    thumbnail_path: Optional[str] = Field(None, description="缩略图路径")
    note: Optional[str] = Field(None, max_length=500, description="备注信息")

    @field_validator('profile_name')
    @classmethod
    def validate_profile_name(cls, v):
        """验证profile名称"""
        if not v or not v.strip():
            raise ValueError('profile_name 不能为空')
        # 检查是否包含非法字符
        if re.search(r'[<>:"/\\|?*]', v):
            raise ValueError('profile_name 包含非法字符')
        return v.strip()

    @field_validator('video_path')
    @classmethod
    def validate_video_path(cls, v):
        """验证视频文件路径"""
        if not v or not v.strip():
            raise ValueError('video_path 不能为空')

        path = Path(v)

        # 检查文件扩展名
        if path.suffix.lower() not in SUPPORTED_VIDEO_FORMATS:
            raise ValueError(f'不支持的视频格式，支持的格式: {", ".join(SUPPORTED_VIDEO_FORMATS)}')

        return v.strip()

    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        """验证标题"""
        # 移除多余的空白字符
        cleaned_title = re.sub(r'\s+', ' ', v.strip())
        return cleaned_title

    @field_validator('language')
    @classmethod
    def validate_language(cls, v):
        """验证语言代码"""
        if not v or not v.strip():
            raise ValueError('language 不能为空')

        # 检查是否为支持的语言代码
        valid_languages = [lang.value for lang in LanguageCode]
        if v not in valid_languages:
            raise ValueError(f'不支持的语言代码，支持的语言: {", ".join(valid_languages)}')

        return v

    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        """验证标签格式"""
        if not v:
            return v

        # 分割标签
        tags_list = [tag.strip() for tag in v.split('|') if tag.strip()]

        # 检查标签数量
        if len(tags_list) > MAX_TAGS_COUNT:
            raise ValueError(f'标签数量不能超过 {MAX_TAGS_COUNT} 个')

        # 检查每个标签的长度
        for tag in tags_list:
            if len(tag) > MAX_TAG_LENGTH:
                raise ValueError(f'标签 "{tag}" 长度不能超过 {MAX_TAG_LENGTH} 字符')

        return '|'.join(tags_list)

    @field_validator('thumbnail_path')
    @classmethod
    def validate_thumbnail_path(cls, v):
        """验证缩略图路径"""
        if not v:
            return v

        path = Path(v)

        # 检查文件扩展名
        if path.suffix.lower() not in SUPPORTED_IMAGE_FORMATS:
            raise ValueError(f'不支持的图片格式，支持的格式: {", ".join(SUPPORTED_IMAGE_FORMATS)}')

        return v.strip()

    @model_validator(mode='after')
    def validate_schedule_time_and_age_restriction(self):
        """验证定时发布时间和年龄限制逻辑"""
        # 验证定时发布时间
        if self.visibility == VisibilityType.SCHEDULED:
            if not self.schedule_time:
                raise ValueError('定时发布时必须提供 schedule_time')

            # 验证时间格式
            try:
                datetime.strptime(self.schedule_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                raise ValueError('schedule_time 格式错误，应为 YYYY-MM-DD HH:MM:SS')

            # 检查时间是否在未来
            schedule_dt = datetime.strptime(self.schedule_time, '%Y-%m-%d %H:%M:%S')
            if schedule_dt <= datetime.now():
                raise ValueError('定时发布时间必须在未来')

        # 验证年龄限制逻辑
        if self.audience_made_for_kids and self.age_restriction_18plus:
            raise ValueError('儿童内容不能设置18+年龄限制')

        return self

    def get_tags_list(self) -> List[str]:
        """获取标签列表"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split('|') if tag.strip()]

    def get_schedule_datetime(self) -> Optional[datetime]:
        """获取定时发布的datetime对象"""
        if not self.schedule_time:
            return None
        try:
            return datetime.strptime(self.schedule_time, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return None

    def is_scheduled(self) -> bool:
        """是否为定时发布"""
        return self.visibility == VisibilityType.SCHEDULED

    def validate_files_exist(self) -> List[str]:
        """验证文件是否存在，返回错误信息列表"""
        errors = []

        # 检查视频文件
        if not Path(self.video_path).exists():
            errors.append(f"视频文件不存在: {self.video_path}")

        # 检查缩略图文件
        if self.thumbnail_path and not Path(self.thumbnail_path).exists():
            errors.append(f"缩略图文件不存在: {self.thumbnail_path}")

        return errors


class ConfigModel(BaseModel):
    """配置数据模型"""

    # 并发设置
    concurrency: int = Field(1, ge=1, le=10, description="并发数")

    # 超时设置
    page_load_timeout: int = Field(30, ge=10, le=300, description="页面加载超时（秒）")
    element_wait_timeout: int = Field(10, ge=5, le=60, description="元素等待超时（秒）")
    upload_process_timeout: int = Field(300, ge=60, le=3600, description="上传处理超时（秒）")
    video_processing_timeout: int = Field(600, ge=300, le=7200, description="视频处理超时（秒）")

    # 重试设置
    max_retry_attempts: int = Field(3, ge=1, le=10, description="最大重试次数")
    retry_delay_seconds: int = Field(3, ge=1, le=60, description="重试延迟（秒）")

    # 操作设置
    operation_interval_seconds: int = Field(10, ge=5, le=300, description="操作间隔（秒）")
    random_delay_min: int = Field(2, ge=1, le=30, description="随机延迟最小值（秒）")
    random_delay_max: int = Field(5, ge=2, le=60, description="随机延迟最大值（秒）")

    # 文件设置
    excel_sheet_name: str = Field("Tasks", min_length=1, max_length=50, description="Excel工作表名")
    result_file: str = Field("upload_results.xlsx", min_length=1, description="结果文件名")
    log_dir: str = Field("logs", min_length=1, description="日志目录")
    screenshot_dir: str = Field("screenshots", min_length=1, description="截图目录")

    # 调试设置
    save_screenshots: bool = Field(True, description="是否保存截图")
    save_page_html: bool = Field(False, description="是否保存页面HTML")
    verbose_logging: bool = Field(True, description="是否详细日志")

    @field_validator('result_file')
    @classmethod
    def validate_result_file(cls, v):
        """验证结果文件名"""
        if not v.endswith(('.xlsx', '.csv')):
            raise ValueError('结果文件必须是 .xlsx 或 .csv 格式')
        return v

    @field_validator('excel_sheet_name', 'log_dir', 'screenshot_dir')
    @classmethod
    def validate_directory_names(cls, v):
        """验证目录和工作表名称"""
        # 检查是否包含非法字符
        if re.search(r'[<>:"/\\|?*]', v):
            raise ValueError(f'名称包含非法字符: {v}')
        return v.strip()

    @model_validator(mode='after')
    def validate_random_delay_range(self):
        """验证随机延迟范围"""
        if self.random_delay_max <= self.random_delay_min:
            raise ValueError('random_delay_max 必须大于 random_delay_min')
        return self

    def get_timeout_dict(self) -> dict:
        """获取超时配置字典"""
        return {
            'page_load': self.page_load_timeout,
            'element_wait': self.element_wait_timeout,
            'upload_process': self.upload_process_timeout,
            'video_processing': self.video_processing_timeout
        }

    def get_retry_config(self) -> dict:
        """获取重试配置字典"""
        return {
            'max_attempts': self.max_retry_attempts,
            'delay_seconds': self.retry_delay_seconds
        }

    def get_operation_config(self) -> dict:
        """获取操作配置字典"""
        return {
            'interval_seconds': self.operation_interval_seconds,
            'random_delay_min': self.random_delay_min,
            'random_delay_max': self.random_delay_max
        }


class ResultModel(BaseModel):
    """结果数据模型"""

    task_id: str = Field(..., description="任务唯一标识")
    profile_name: str = Field(..., description="使用的 profile")
    video_path: str = Field(..., description="视频文件路径")
    status: TaskStatus = Field(..., description="任务状态")

    # 成功时的字段
    video_url: Optional[str] = Field(None, description="视频链接")
    video_id: Optional[str] = Field(None, description="视频ID")

    # 失败时的字段
    error_message: Optional[str] = Field(None, description="错误信息")
    screenshot_path: Optional[str] = Field(None, description="异常截图路径")
    diagnostic_info: Optional[Dict[str, Any]] = Field(None, description="诊断信息")

    # 时间字段
    started_at: datetime = Field(default_factory=datetime.now, description="任务开始时间")
    finished_at: Optional[datetime] = Field(None, description="任务完成时间")
    duration_seconds: Optional[float] = Field(None, description="执行耗时")

    # 额外信息字段
    retry_count: int = Field(0, ge=0, description="重试次数")
    last_error: Optional[str] = Field(None, description="最后一次错误信息")

    @field_validator('video_url')
    @classmethod
    def validate_video_url(cls, v):
        """验证视频链接格式"""
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('视频链接格式无效')
        return v

    @field_validator('video_id')
    @classmethod
    def validate_video_id(cls, v):
        """验证视频ID格式"""
        if v and not re.match(r'^[a-zA-Z0-9_-]{6,20}$', v):
            raise ValueError('YouTube视频ID格式无效')
        return v

    def calculate_duration(self):
        """计算执行耗时"""
        if self.finished_at:
            self.duration_seconds = (self.finished_at - self.started_at).total_seconds()

    def mark_completed(self, status: TaskStatus, video_url: str = None, video_id: str = None,
                      error_message: str = None, screenshot_path: str = None):
        """标记任务完成"""
        self.status = status
        self.finished_at = datetime.now()
        self.calculate_duration()

        if status == TaskStatus.SUCCESS:
            self.video_url = video_url
            self.video_id = video_id
        else:
            self.error_message = error_message
            self.screenshot_path = screenshot_path

    def add_retry(self, error_message: str = None):
        """增加重试次数"""
        self.retry_count += 1
        if error_message:
            self.last_error = error_message

    def is_success(self) -> bool:
        """是否成功"""
        return self.status == TaskStatus.SUCCESS

    def is_failed(self) -> bool:
        """是否失败"""
        return self.status == TaskStatus.FAILED

    def needs_verification(self) -> bool:
        """是否需要人工验证"""
        return self.status == TaskStatus.NEEDS_VERIFICATION

    def get_duration_formatted(self) -> str:
        """获取格式化的执行时间"""
        if not self.duration_seconds:
            return "未知"

        if self.duration_seconds < 60:
            return f"{self.duration_seconds:.1f}秒"
        elif self.duration_seconds < 3600:
            minutes = self.duration_seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = self.duration_seconds / 3600
            return f"{hours:.1f}小时"

    def get_summary(self) -> str:
        """获取结果摘要"""
        status_map = {
            TaskStatus.SUCCESS: "✅ 成功",
            TaskStatus.FAILED: "❌ 失败",
            TaskStatus.NEEDS_VERIFICATION: "⚠️ 需要验证",
            TaskStatus.PENDING: "⏳ 等待中",
            TaskStatus.RUNNING: "🔄 运行中"
        }

        status_text = status_map.get(self.status, str(self.status))
        duration_text = self.get_duration_formatted()

        summary = f"{status_text} | 耗时: {duration_text}"

        if self.retry_count > 0:
            summary += f" | 重试: {self.retry_count}次"

        return summary


class ProgressModel(BaseModel):
    """进度数据模型"""

    total_tasks: int = Field(..., ge=0, description="总任务数")
    completed_tasks: int = Field(0, ge=0, description="已完成任务数")
    success_tasks: int = Field(0, ge=0, description="成功任务数")
    failed_tasks: int = Field(0, ge=0, description="失败任务数")
    verification_tasks: int = Field(0, ge=0, description="需要验证的任务数")
    current_profile: Optional[str] = Field(None, description="当前处理的profile")
    current_task: Optional[str] = Field(None, description="当前任务描述")

    @model_validator(mode='after')
    def validate_task_counts(self):
        """验证任务计数不超过总数"""
        if self.completed_tasks > self.total_tasks:
            raise ValueError('已完成任务数不能超过总任务数')
        if self.success_tasks > self.total_tasks:
            raise ValueError('成功任务数不能超过总任务数')
        if self.failed_tasks > self.total_tasks:
            raise ValueError('失败任务数不能超过总任务数')
        if self.verification_tasks > self.total_tasks:
            raise ValueError('需验证任务数不能超过总任务数')
        return self

    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.total_tasks == 0:
            return 0.0
        return (self.completed_tasks / self.total_tasks) * 100

    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.completed_tasks == 0:
            return 0.0
        return (self.success_tasks / self.completed_tasks) * 100

    def update_progress(self, result: ResultModel):
        """根据结果更新进度"""
        self.completed_tasks += 1

        if result.is_success():
            self.success_tasks += 1
        elif result.is_failed():
            self.failed_tasks += 1
        elif result.needs_verification():
            self.verification_tasks += 1

    def get_status_summary(self) -> str:
        """获取状态摘要"""
        progress = self.get_progress_percentage()
        success_rate = self.get_success_rate()

        return (f"进度: {self.completed_tasks}/{self.total_tasks} ({progress:.1f}%) | "
                f"成功率: {success_rate:.1f}% | "
                f"成功: {self.success_tasks} | 失败: {self.failed_tasks} | "
                f"需验证: {self.verification_tasks}")


class ErrorModel(BaseModel):
    """错误信息模型"""

    error_type: str = Field(..., description="错误类型")
    error_message: str = Field(..., description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误发生时间")
    context: Optional[dict] = Field(None, description="错误上下文信息")

    def get_formatted_message(self) -> str:
        """获取格式化的错误信息"""
        timestamp_str = self.timestamp.strftime('%Y-%m-%d %H:%M:%S')
        message = f"[{timestamp_str}] {self.error_type}: {self.error_message}"

        if self.error_code:
            message += f" (代码: {self.error_code})"

        return message


class ValidationResult(BaseModel):
    """验证结果模型"""

    is_valid: bool = Field(..., description="是否验证通过")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")
    warnings: List[str] = Field(default_factory=list, description="警告信息列表")

    def add_error(self, message: str):
        """添加错误信息"""
        self.errors.append(message)
        self.is_valid = False

    def add_warning(self, message: str):
        """添加警告信息"""
        self.warnings.append(message)

    def get_summary(self) -> str:
        """获取验证摘要"""
        if self.is_valid:
            summary = "✅ 验证通过"
            if self.warnings:
                summary += f" (警告: {len(self.warnings)}个)"
        else:
            summary = f"❌ 验证失败 (错误: {len(self.errors)}个"
            if self.warnings:
                summary += f", 警告: {len(self.warnings)}个"
            summary += ")"

        return summary
