#!/usr/bin/env python3
"""
实时页面监控工具
持续监控页面变化，快速定位问题
"""

import sys
import asyncio
import time
from pathlib import Path
from playwright.async_api import async_playwright

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

async def monitor_page_realtime():
    """实时监控页面"""
    print("🔍 启动实时页面监控...")
    
    try:
        # 连接到已打开的比特浏览器
        from youtube_uploader.executor import TaskExecutor
        from youtube_uploader.excel_handler import ExcelHandler
        
        # 读取任务
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        task = tasks[0]
        
        # 初始化执行器
        config = {'max_retries': 3}
        executor = TaskExecutor(config)
        
        # 打开浏览器
        print(f"📱 连接到浏览器 profile: {task.profile_name}")
        ws_address = executor.bit_browser_manager.open_browser(task.profile_name)
        
        if not ws_address:
            print("❌ 无法打开浏览器")
            return
            
        print(f"✅ 浏览器已连接: {ws_address}")
        
        # 连接到浏览器
        async with async_playwright() as p:
            browser = await p.chromium.connect_over_cdp(ws_address)
            context = browser.contexts[0] if browser.contexts else await browser.new_context()
            page = await context.new_page()
            
            print("\n🌐 导航到YouTube上传页面...")
            await page.goto('https://www.youtube.com/upload', wait_until='networkidle')
            
            print("\n📊 开始实时监控...")
            print("按 Ctrl+C 停止监控")
            
            # 监控计数器
            iteration = 0
            
            try:
                while True:
                    iteration += 1
                    print(f"\n--- 监控周期 {iteration} ({time.strftime('%H:%M:%S')}) ---")
                    
                    # 1. 检查页面基本状态
                    title = await page.title()
                    url = page.url
                    print(f"📄 页面: {title}")
                    print(f"🔗 URL: {url}")
                    
                    # 2. 检查关键元素状态
                    key_elements = {
                        '文件上传': 'input[type="file"]',
                        '标题输入': 'div[contenteditable="true"]',
                        '发布按钮': '[aria-label*="publish" i], [aria-label*="发布" i]',
                        '进度条': '[role="progressbar"]',
                        '加载指示器': '[aria-label*="loading" i], .loading, .spinner',
                        '错误信息': '[role="alert"], .error'
                    }
                    
                    for name, selector in key_elements.items():
                        try:
                            elements = await page.query_selector_all(selector)
                            if elements:
                                visible_count = 0
                                for elem in elements:
                                    if await elem.is_visible():
                                        visible_count += 1
                                
                                if visible_count > 0:
                                    print(f"✅ {name}: {visible_count} 个可见元素")
                                    
                                    # 获取第一个可见元素的文本
                                    for elem in elements:
                                        if await elem.is_visible():
                                            text = await elem.text_content()
                                            if text and text.strip():
                                                print(f"   📝 文本: {text.strip()[:50]}...")
                                            break
                                else:
                                    print(f"⚪ {name}: {len(elements)} 个元素（不可见）")
                            else:
                                print(f"❌ {name}: 未找到")
                        except Exception as e:
                            print(f"⚠️ {name}: 检查失败 - {e}")
                    
                    # 3. 检查网络活动
                    network_requests = []
                    
                    def handle_request(request):
                        if any(keyword in request.url.lower() for keyword in ['upload', 'youtube', 'video']):
                            network_requests.append({
                                'url': request.url,
                                'method': request.method,
                                'type': request.resource_type
                            })
                    
                    page.on('request', handle_request)
                    
                    # 等待2秒监控网络
                    await asyncio.sleep(2)
                    
                    if network_requests:
                        print(f"🌐 网络活动: {len(network_requests)} 个请求")
                        for req in network_requests[-3:]:  # 显示最后3个
                            print(f"   📡 {req['method']} {req['type']}: {req['url'][:60]}...")
                    else:
                        print("🌐 网络活动: 无相关请求")
                    
                    # 4. 检查页面性能
                    try:
                        performance = await page.evaluate('''() => {
                            const perf = performance.getEntriesByType('navigation')[0];
                            return {
                                loadTime: perf.loadEventEnd - perf.loadEventStart,
                                domContentLoaded: perf.domContentLoadedEventEnd - perf.domContentLoadedEventStart,
                                responseTime: perf.responseEnd - perf.responseStart
                            };
                        }''')
                        print(f"⚡ 性能: 加载 {performance['loadTime']:.0f}ms, DOM {performance['domContentLoaded']:.0f}ms")
                    except:
                        pass
                    
                    # 5. 检查控制台错误
                    try:
                        console_errors = await page.evaluate('''() => {
                            return window.console_errors || [];
                        }''')
                        if console_errors:
                            print(f"🚨 控制台错误: {len(console_errors)} 个")
                    except:
                        pass
                    
                    print("⏳ 等待5秒后继续监控...")
                    await asyncio.sleep(5)
                    
            except KeyboardInterrupt:
                print("\n\n🛑 监控已停止")
            
            await page.close()
            await browser.close()
            
    except Exception as e:
        print(f"❌ 监控失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 YouTube页面实时监控工具")
    print("=" * 60)
    
    asyncio.run(monitor_page_realtime())
