import asyncio
import re
from playwright.async_api import Playwright, async_playwright, expect


async def run(playwright: Playwright) -> None:
    browser = await playwright.chromium.launch(headless=False)
    context = await browser.new_context()
    page = await context.new_page()
    await page.goto("https://accounts.google.com/v3/signin/identifier?continue=https%3A%2F%2Fwww.youtube.com%2Fsignin%3Faction_handle_signin%3Dtrue%26app%3Ddesktop%26hl%3Dzh-CN%26next%3Dhttps%253A%252F%252Fstudio.youtube.com%252Fchannel%252FUC%252Fvideos%253Fd%253Dud%26feature%3Dredirect_login&hl=zh-CN&ifkv=AdBytiPwYuPRirCv0qxh-5nKa2CRhyZ8yxgw2FoZDoy9XC6l6A0HjAN0NyS0kTYfgNviGL-WtSenXA&passive=true&service=youtube&uilel=3&flowName=GlifWebSignIn&flowEntry=ServiceLogin&dsh=S1090676184%3A1755177490812643")
    await page.get_by_role("textbox", name="邮箱或电话号码").click()
    await page.get_by_role("textbox", name="邮箱或电话号码").fill("<EMAIL>")
    await page.get_by_role("button", name="下一步").click()
    await page.get_by_role("link", name="重试").click()
    await page.get_by_role("textbox", name="邮箱或电话号码").click()
    await page.get_by_role("textbox", name="邮箱或电话号码").fill("<EMAIL>")
    await page.get_by_role("button", name="下一步").click()
    async with page.expect_popup() as page1_info:
        await page.get_by_role("link", name="了解详情").click()
    page1 = await page1_info.value
    await page1.close()
    await page.close()

    # ---------------------
    await context.close()
    await browser.close()


async def main() -> None:
    async with async_playwright() as playwright:
        await run(playwright)


asyncio.run(main())
