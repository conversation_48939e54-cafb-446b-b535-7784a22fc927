# YouTube Shorts 批量上传自动化 - 技术设计文档

## 1. 项目概述

基于比特浏览器 + Playwright 的 YouTube Shorts 批量上传自动化工具，支持多账号并发操作，从 Excel 读取任务清单，自动完成视频上传、信息填写和发布流程。

## 2. 技术架构

### 2.1 核心技术栈
- **Python 3.x** - 主要开发语言
- **Playwright** - 浏览器自动化（通过 CDP 连接比特浏览器）
- **比特浏览器** - 多账号管理和指纹隔离
- **openpyxl** - Excel 文件读写
- **pydantic** - 数据校验和模型
- **tenacity** - 重试机制
- **loguru** - 日志记录

### 2.2 项目结构
```
youtube_uploader/
├── __init__.py
├── config.py              # 配置管理
├── models.py              # 数据模型（Pydantic）
├── bit_browser.py         # 比特浏览器集成
├── youtube_automation.py  # YouTube自动化核心逻辑
├── excel_handler.py       # Excel读写处理
├── runner.py              # 主执行器
├── utils.py               # 工具函数
└── selectors.py           # 界面选择器映射
```

## 3. 核心设计决策

### 3.1 比特浏览器集成
- 使用现有 `bit_api.py` 中的 `BitBrowser` 类
- 通过 `browser_list()` 检查 profile 是否存在（不创建新 profile）
- 使用 `open_browser()` 获取 WebSocket 调试地址
- 通过 `chromium.connect_over_cdp(ws)` 连接 Playwright

### 3.2 界面元素定位策略
- **开发阶段**：使用 Playwright MCP 实时分析 YouTube 界面，识别稳定的元素定位策略
- **生产阶段**：基于 MCP 分析结果，编写标准的 Playwright 选择器代码
- **维护阶段**：当 YouTube 界面更新时，重新用 MCP 分析并更新选择器

### 3.3 任务执行策略
- **隔离性**：每个任务在新的 tab 中执行，避免相互影响
- **并发性**：支持不同 profile 并发执行，同一 profile 内串行执行
- **稳定性**：操作间隔 10 秒，加入随机延时（2-5秒）
- **容错性**：网络问题重试 3 次，验证码/异常记录日志并跳过

## 4. 数据模型设计

### 4.1 Excel 任务清单字段
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| profile_name | str | ✓ | 比特浏览器 profile 名称 |
| video_path | str | ✓ | 视频文件绝对路径（mp4格式） |
| title | str | ✓ | 标题（最大100字符） |
| description | str | ✗ | 描述（无长度限制） |
| tags | str | ✗ | 标签（用\|分隔） |
| visibility | str | ✓ | public\|unlisted\|private\|scheduled |
| schedule_time | str | ✗ | 定时发布时间（visibility=scheduled时必填） |
| audience_made_for_kids | bool | ✓ | 是否为儿童内容 |
| age_restriction_18plus | bool | ✓ | 是否18+限制 |
| playlist | str | ✗ | 播放列表名称 |
| allow_remix | bool | ✓ | 是否允许混剪 |
| language | str | ✓ | 语言代码（如zh-CN, en-US） |
| thumbnail_path | str | ✗ | 缩略图路径（可选） |
| note | str | ✗ | 备注信息 |

### 4.2 结果记录字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| task_id | str | 任务唯一标识 |
| profile_name | str | 使用的 profile |
| video_path | str | 视频文件路径 |
| status | str | success\|failed\|needs_verification |
| video_url | str | 成功时的视频链接 |
| video_id | str | 成功时的视频ID |
| error_message | str | 失败时的错误信息 |
| screenshot_path | str | 异常截图路径 |
| started_at | datetime | 任务开始时间 |
| finished_at | datetime | 任务完成时间 |
| duration_seconds | float | 执行耗时 |

## 5. 配置设计

### 5.1 配置文件结构（config.yml）
```yaml
# 并发设置
concurrency: 1

# 超时设置（秒）
timeouts:
  page_load: 30
  element_wait: 10
  upload_process: 300
  video_processing: 600

# 重试设置
retry:
  max_attempts: 3
  delay_seconds: 3

# 操作设置
operation:
  interval_seconds: 10
  random_delay_range: [2, 5]

# 文件设置
files:
  excel_sheet_name: "Tasks"
  result_file: "upload_results.xlsx"
  log_dir: "logs"
  screenshot_dir: "screenshots"

# 调试设置
debug:
  save_screenshots: true
  save_page_html: false
  verbose_logging: true

# 环境变量覆盖支持
# 格式：YOUTUBE_UPLOADER_<SECTION>_<KEY>
# 例如：YOUTUBE_UPLOADER_CONCURRENCY=2
```

## 6. 错误处理策略

### 6.1 错误分类
- **网络错误**：重试 3 次，间隔 3 秒
- **元素定位失败**：记录详细日志和截图，标记失败
- **验证码/人机验证**：标记 "needs_verification"，记录截图
- **账号异常**：记录日志，跳过该 profile 的后续任务
- **文件错误**：验证文件存在性和格式，记录错误信息

### 6.2 日志记录
- **控制台输出**：每步操作的进度和耗时
- **文件日志**：详细的操作记录和错误信息
- **截图保存**：异常情况自动截图
- **结果记录**：所有任务的执行结果写入 Excel/CSV

## 7. 安全和合规

### 7.1 风控策略
- 操作间隔：固定 10 秒 + 随机 2-5 秒
- 指纹隔离：依赖比特浏览器的 profile 隔离
- 行为模拟：模拟人工操作的节奏和路径

### 7.2 限制处理
- 单个 profile 每日上传限制：10 个视频（暂不强制检查）
- 平台审核：视频被标记审核时记录日志，不做特殊处理
- 账号限制：遇到限制时记录日志并跳过

## 8. 部署和运行

### 8.1 环境要求
- Windows 操作系统
- Python 3.8+
- 比特浏览器已安装并运行
- 各 profile 已完成 YouTube 登录

### 8.2 命令行接口
```bash
# 基本运行
python -m youtube_uploader.runner --excel tasks.xlsx

# 指定并发数
python -m youtube_uploader.runner --excel tasks.xlsx --concurrency 2

# 指定配置文件
python -m youtube_uploader.runner --excel tasks.xlsx --config custom_config.yml

# 仅处理特定 profile
python -m youtube_uploader.runner --excel tasks.xlsx --profiles "profile1,profile2"
```

## 9. 开发阶段计划

### 9.1 MCP 界面分析阶段
1. 连接到已登录的 YouTube 账号
2. 分析完整的上传流程界面
3. 识别关键元素的稳定选择器
4. 测试中英文界面差异
5. 记录特殊情况处理方案

### 9.2 代码开发阶段
1. 搭建项目框架和数据模型
2. 实现比特浏览器集成
3. 基于 MCP 分析结果开发自动化流程
4. 实现 Excel 处理和结果记录
5. 添加错误处理和日志记录
6. 集成测试和优化

### 9.3 测试和优化阶段
1. 单任务功能测试
2. 多任务并发测试
3. 异常情况测试
4. 性能优化和稳定性改进
