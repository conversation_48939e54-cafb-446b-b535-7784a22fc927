#!/usr/bin/env python3
"""
使用Playwright录制YouTube上传流程
生成准确的自动化脚本
"""

import sys
import asyncio
import subprocess
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def record_with_existing_browser():
    """使用现有的比特浏览器进行录制"""
    print("🎬 启动Playwright录制器...")
    print("=" * 60)
    
    try:
        # 获取比特浏览器的WebSocket地址
        from youtube_uploader.executor import TaskExecutor
        from youtube_uploader.excel_handler import ExcelHandler
        
        # 读取任务
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        task = tasks[0]
        
        # 初始化执行器
        config = {'max_retries': 3}
        executor = TaskExecutor(config)
        
        # 打开浏览器
        print(f"📱 连接到浏览器 profile: {task.profile_name}")
        ws_address = executor.bit_browser_manager.open_browser(task.profile_name)
        
        if not ws_address:
            print("❌ 无法打开浏览器")
            return
            
        print(f"✅ 浏览器WebSocket: {ws_address}")
        
        # 提取端口号
        import re
        port_match = re.search(r':(\d+)/', ws_address)
        if not port_match:
            print("❌ 无法提取端口号")
            return
            
        port = port_match.group(1)
        print(f"🔌 调试端口: {port}")
        
        # 启动Playwright录制器连接到现有浏览器
        print("\n🎬 启动录制器...")
        print("📋 录制步骤:")
        print("1. 录制器将打开并连接到您的比特浏览器")
        print("2. 手动执行YouTube上传流程")
        print("3. 录制完成后关闭录制器")
        print("4. 生成的代码将保存为 recorded_upload.py")
        print("\n按回车键启动录制器...")
        input()
        
        # 使用playwright codegen连接到现有浏览器
        cmd = [
            'playwright', 'codegen',
            '--target', 'python-async',
            '--output', 'recorded_upload.py',
            f'--browser-ws-endpoint=ws://127.0.0.1:{port}/devtools/browser',
            'https://www.youtube.com/upload'
        ]
        
        print(f"🚀 执行命令: {' '.join(cmd)}")
        
        # 运行录制器
        result = subprocess.run(cmd, capture_output=False)
        
        if result.returncode == 0:
            print("\n✅ 录制完成！")
            print("📁 生成的文件: recorded_upload.py")
            
            # 检查文件是否存在
            if Path('recorded_upload.py').exists():
                print("✅ 录制文件已生成")
                
                # 显示文件内容预览
                with open('recorded_upload.py', 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    print(f"\n📄 文件预览 (前20行):")
                    for i, line in enumerate(lines[:20]):
                        print(f"{i+1:2d}: {line}")
                    
                    if len(lines) > 20:
                        print(f"... (还有 {len(lines)-20} 行)")
                        
            else:
                print("⚠️ 录制文件未找到")
        else:
            print(f"❌ 录制失败，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"❌ 录制过程出错: {e}")
        import traceback
        traceback.print_exc()

def record_with_new_browser():
    """使用新浏览器进行录制（备用方案）"""
    print("🎬 使用新浏览器录制...")
    
    try:
        # 直接启动录制器
        cmd = [
            'playwright', 'codegen',
            '--target', 'python-async',
            '--output', 'recorded_upload_new.py',
            'https://www.youtube.com/upload'
        ]
        
        print(f"🚀 执行命令: {' '.join(cmd)}")
        print("📋 请在打开的浏览器中:")
        print("1. 登录YouTube账号")
        print("2. 执行完整的视频上传流程")
        print("3. 录制完成后关闭浏览器")
        
        # 运行录制器
        result = subprocess.run(cmd, capture_output=False)
        
        if result.returncode == 0:
            print("\n✅ 录制完成！")
            print("📁 生成的文件: recorded_upload_new.py")
        else:
            print(f"❌ 录制失败，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"❌ 录制过程出错: {e}")

def main():
    """主函数"""
    print("🎬 Playwright录制工具")
    print("=" * 60)
    
    print("选择录制方式:")
    print("1. 使用现有的比特浏览器录制 (推荐)")
    print("2. 使用新浏览器录制")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == '1':
        record_with_existing_browser()
    elif choice == '2':
        record_with_new_browser()
    elif choice == '3':
        print("👋 退出")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
