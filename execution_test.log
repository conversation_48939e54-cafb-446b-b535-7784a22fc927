2025-08-14 13:23:25,123 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-14 13:23:27,086 - faker.factory - DEBUG - Not in REPL -> leaving logger event level as is.
2025-08-14 13:23:27,096 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.address`.
2025-08-14 13:23:27,108 - faker.factory - DEBUG - Provider `faker.providers.address` has been localized to `en_US`.
2025-08-14 13:23:27,112 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.automotive`.
2025-08-14 13:23:27,118 - faker.factory - DEBUG - Provider `faker.providers.automotive` has been localized to `en_US`.
2025-08-14 13:23:27,119 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.bank`.
2025-08-14 13:23:27,142 - faker.factory - DEBUG - Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider.
2025-08-14 13:23:27,145 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.barcode`.
2025-08-14 13:23:27,146 - faker.factory - DEBUG - Provider `faker.providers.barcode` has been localized to `en_US`.
2025-08-14 13:23:27,149 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.color`.
2025-08-14 13:23:27,153 - faker.factory - DEBUG - Provider `faker.providers.color` has been localized to `en_US`.
2025-08-14 13:23:27,154 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.company`.
2025-08-14 13:23:27,175 - faker.factory - DEBUG - Provider `faker.providers.company` has been localized to `en_US`.
2025-08-14 13:23:27,178 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.credit_card`.
2025-08-14 13:23:27,180 - faker.factory - DEBUG - Provider `faker.providers.credit_card` has been localized to `en_US`.
2025-08-14 13:23:27,182 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.currency`.
2025-08-14 13:23:27,187 - faker.factory - DEBUG - Provider `faker.providers.currency` has been localized to `en_US`.
2025-08-14 13:23:27,189 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.date_time`.
2025-08-14 13:23:27,196 - faker.factory - DEBUG - Provider `faker.providers.date_time` has been localized to `en_US`.
2025-08-14 13:23:27,204 - faker.factory - DEBUG - Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-14 13:23:27,204 - faker.factory - DEBUG - Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-14 13:23:27,206 - faker.factory - DEBUG - Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-14 13:23:27,207 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.geo`.
2025-08-14 13:23:27,212 - faker.factory - DEBUG - Provider `faker.providers.geo` has been localized to `en_US`.
2025-08-14 13:23:27,213 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.internet`.
2025-08-14 13:23:27,219 - faker.factory - DEBUG - Provider `faker.providers.internet` has been localized to `en_US`.
2025-08-14 13:23:27,220 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.isbn`.
2025-08-14 13:23:27,223 - faker.factory - DEBUG - Provider `faker.providers.isbn` has been localized to `en_US`.
2025-08-14 13:23:27,227 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.job`.
2025-08-14 13:23:27,238 - faker.factory - DEBUG - Provider `faker.providers.job` has been localized to `en_US`.
2025-08-14 13:23:27,242 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.lorem`.
2025-08-14 13:23:27,247 - faker.factory - DEBUG - Provider `faker.providers.lorem` has been localized to `en_US`.
2025-08-14 13:23:27,248 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.misc`.
2025-08-14 13:23:27,250 - faker.factory - DEBUG - Provider `faker.providers.misc` has been localized to `en_US`.
2025-08-14 13:23:27,252 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.passport`.
2025-08-14 13:23:27,253 - faker.factory - DEBUG - Provider `faker.providers.passport` has been localized to `en_US`.
2025-08-14 13:23:27,254 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.person`.
2025-08-14 13:23:27,276 - faker.factory - DEBUG - Provider `faker.providers.person` has been localized to `en_US`.
2025-08-14 13:23:27,279 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.phone_number`.
2025-08-14 13:23:27,286 - faker.factory - DEBUG - Provider `faker.providers.phone_number` has been localized to `en_US`.
2025-08-14 13:23:27,289 - faker.factory - DEBUG - Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-14 13:23:27,290 - faker.factory - DEBUG - Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-14 13:23:27,292 - faker.factory - DEBUG - Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-14 13:23:27,293 - faker.factory - DEBUG - Looking for locale `en_US` in provider `faker.providers.ssn`.
2025-08-14 13:23:27,307 - faker.factory - DEBUG - Provider `faker.providers.ssn` has been localized to `en_US`.
2025-08-14 13:23:27,310 - faker.factory - DEBUG - Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-14 13:23:27,317 - youtube_uploader.excel_handler - INFO - 开始读取文件: tasks_template.xlsx
2025-08-14 13:23:27,353 - youtube_uploader.excel_handler - INFO - 发现表头: ['profile_name', 'video_path', 'title', 'description', 'tags', 'visibility', 'schedule_time', 'audience_made_for_kids', 'age_restriction_18plus', 'playlist', 'allow_remix', 'language', 'thumbnail_path', 'note']
2025-08-14 13:23:27,353 - youtube_uploader.excel_handler - INFO - 成功解析 1 个有效任务
2025-08-14 13:23:27,354 - youtube_uploader.bit_browser - INFO - 比特浏览器管理器初始化成功，网站: YouTube
2025-08-14 13:23:27,359 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 127.0.0.1:54345
2025-08-14 13:23:27,363 - urllib3.connectionpool - DEBUG - http://127.0.0.1:54345 "GET / HTTP/1.1" 200 8753
2025-08-14 13:23:27,372 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 127.0.0.1:54345
2025-08-14 13:23:27,376 - urllib3.connectionpool - DEBUG - http://127.0.0.1:54345 "GET / HTTP/1.1" 200 8753
2025-08-14 13:23:27,377 - bit_api - INFO - 查询窗口: Liberty Narratives
2025-08-14 13:23:27,378 - bit_api - INFO - 开始请求接口: http://127.0.0.1:54345/browser/list
2025-08-14 13:23:27,379 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 127.0.0.1:54345
2025-08-14 13:23:27,501 - urllib3.connectionpool - DEBUG - http://127.0.0.1:54345 "POST /browser/list HTTP/1.1" 200 72
2025-08-14 13:23:27,502 - youtube_uploader.bit_browser - WARNING - Profile 'Liberty Narratives' 不存在
2025-08-14 13:23:27,503 - bit_api - INFO - 查询窗口: 
2025-08-14 13:23:27,504 - bit_api - INFO - 开始请求接口: http://127.0.0.1:54345/browser/list
2025-08-14 13:23:27,505 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 127.0.0.1:54345
2025-08-14 13:23:27,636 - urllib3.connectionpool - DEBUG - http://127.0.0.1:54345 "POST /browser/list HTTP/1.1" 200 21489
2025-08-14 13:23:27,640 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 127.0.0.1:54345
2025-08-14 13:23:27,643 - urllib3.connectionpool - DEBUG - http://127.0.0.1:54345 "GET / HTTP/1.1" 200 8753
2025-08-14 13:23:27,644 - bit_api - INFO - 查询窗口: Liberty Narratives
2025-08-14 13:23:27,644 - bit_api - INFO - 开始请求接口: http://127.0.0.1:54345/browser/list
2025-08-14 13:23:27,645 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 127.0.0.1:54345
2025-08-14 13:23:27,739 - urllib3.connectionpool - DEBUG - http://127.0.0.1:54345 "POST /browser/list HTTP/1.1" 200 72
2025-08-14 13:23:27,740 - youtube_uploader.bit_browser - WARNING - Profile 'Liberty Narratives' 不存在
