"""
基于录制数据的优化YouTube自动化模块
使用真实的页面选择器，解决性能问题
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from pathlib import Path
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError

from .models import TaskModel, ResultModel, TaskStatus, VisibilityType
from .utils import random_delay, log_step, take_screenshot


class OptimizedYouTubeAutomation:
    """优化的YouTube自动化类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化"""
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 基于录制数据的选择器
        self.selectors = {
            # 文件上传
            'file_picker_label': '.label.style-scope.ytcp-uploads-file-picker',
            'file_input': 'div#content > input',
            
            # 标题输入
            'title_textbox': '#textbox',
            'title_container': '#container-content',
            
            # 描述输入
            'description_textbox': '#textbox',  # 可能需要更具体的选择器
            
            # 儿童内容设置
            'kids_dropdown_trigger': '.dropdown-trigger-text.style-scope.ytcp-text-dropdown-trigger',
            'kids_no_option': '.style-scope.ytkc-made-for-kids-select',
            
            # 年龄限制
            'age_restriction_expand': '.expand-button.remove-default-style.style-scope.ytcp-video-metadata-editor-basics',
            'age_restriction_no': '.style-scope.ytcp-age-restriction-select',
            
            # 语言设置
            'language_dropdown': '.right-container.style-scope.ytcp-dropdown-trigger',
            'language_english': '.item-text.main-text.style-scope.ytcp-text-menu',
            
            # 混剪设置
            'remix_audio_only': '.style-scope.ytcp-video-metadata-remix-settings',
            
            # 通用按钮
            'generic_button': '.yt-spec-touch-feedback-shape__fill',
            'radio_off': '#offRadio',
            'text_input': '#text-input',
            
            # 发布相关
            'next_button': '[aria-label="Next"], button:has-text("Next"), button:has-text("下一步")',
            'publish_button': '[aria-label="Publish"], button:has-text("Publish"), button:has-text("发布")',
            'done_button': '[aria-label="Done"], button:has-text("Done"), button:has-text("完成")',
            
            # 进度和状态
            'progress_bar': '[role="progressbar"]',
            'upload_progress': '.progress-bar, .upload-progress',
            'processing_status': '.processing-status, .upload-status',
            
            # 可见性设置
            'visibility_button': '[aria-label*="visibility"], [aria-label*="可见性"]',
            'visibility_public': 'text="Public"',
            'visibility_unlisted': 'text="Unlisted"',
            'visibility_private': 'text="Private"',
        }
    
    async def upload_video(self, page: Page, task: TaskModel) -> ResultModel:
        """上传视频到YouTube"""
        self.logger.info(f"开始上传视频: {task.title}")
        
        try:
            # 1. 导航到上传页面
            await self._navigate_to_upload_page(page)
            
            # 2. 上传文件
            await self._upload_file(page, task.video_path)
            
            # 3. 等待上传完成
            await self._wait_for_upload_complete(page)
            
            # 4. 填写视频信息
            await self._fill_video_details(page, task)
            
            # 5. 设置可见性和其他选项
            await self._configure_video_settings(page, task)
            
            # 6. 发布视频
            video_url = await self._publish_video(page, task)
            
            # 7. 创建成功结果
            result = ResultModel(
                task_id=task.get_task_id(),
                profile_name=task.profile_name,
                video_path=task.video_path,
                status=TaskStatus.SUCCESS,
                video_url=video_url,
                video_id=self._extract_video_id(video_url) if video_url else None
            )
            
            self.logger.info(f"视频上传成功: {video_url}")
            return result
            
        except Exception as e:
            self.logger.error(f"视频上传失败: {e}")
            
            # 截图保存错误状态
            screenshot_path = await take_screenshot(
                page, 
                self.config.get('screenshot_dir', 'screenshots'),
                f"upload_error_{task.profile_name}"
            )
            
            # 创建失败结果
            result = ResultModel(
                task_id=task.get_task_id(),
                profile_name=task.profile_name,
                video_path=task.video_path,
                status=TaskStatus.FAILED,
                error_message=str(e),
                screenshot_path=screenshot_path
            )
            
            return result
    
    async def _navigate_to_upload_page(self, page: Page):
        """导航到上传页面"""
        self.logger.info("导航到YouTube上传页面")
        
        await page.goto('https://www.youtube.com/upload', wait_until='networkidle')
        await page.wait_for_timeout(2000)
        
        # 检查是否需要登录
        if 'accounts.google.com' in page.url or 'signin' in page.url:
            raise Exception("需要登录YouTube账号")
    
    async def _upload_file(self, page: Page, video_path: str):
        """上传视频文件"""
        self.logger.info(f"上传视频文件: {video_path}")
        
        if not Path(video_path).exists():
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        try:
            # 方法1: 直接查找文件输入元素
            file_input = await page.wait_for_selector('input[type="file"]', timeout=10000)
            await file_input.set_input_files(video_path)
            self.logger.info("✅ 文件上传成功")
            
        except PlaywrightTimeoutError:
            # 方法2: 点击上传区域然后上传
            try:
                await page.click(self.selectors['file_picker_label'])
                await page.wait_for_timeout(1000)
                
                file_input = await page.wait_for_selector('input[type="file"]', timeout=5000)
                await file_input.set_input_files(video_path)
                self.logger.info("✅ 通过点击上传区域成功上传文件")
                
            except Exception as e:
                raise Exception(f"文件上传失败: {e}")
    
    async def _wait_for_upload_complete(self, page: Page):
        """等待文件上传完成"""
        self.logger.info("等待文件上传完成...")
        
        # 等待上传进度条出现
        try:
            await page.wait_for_selector(self.selectors['progress_bar'], timeout=30000)
            self.logger.info("上传进度条已出现")
        except PlaywrightTimeoutError:
            self.logger.warning("未找到进度条，继续执行")
        
        # 监控上传进度
        max_wait_time = self.config.get('upload_timeout', 600)  # 默认10分钟
        start_time = asyncio.get_event_loop().time()
        
        while True:
            current_time = asyncio.get_event_loop().time()
            if current_time - start_time > max_wait_time:
                raise TimeoutError(f"上传超时 ({max_wait_time}秒)")
            
            # 检查是否有进度条
            progress_bars = await page.query_selector_all(self.selectors['progress_bar'])
            
            if progress_bars:
                # 获取进度值
                for bar in progress_bars:
                    try:
                        progress_value = await bar.get_attribute('aria-valuenow')
                        progress_max = await bar.get_attribute('aria-valuemax')
                        
                        if progress_value and progress_max:
                            progress_percent = (float(progress_value) / float(progress_max)) * 100
                            self.logger.info(f"上传进度: {progress_percent:.1f}%")
                            
                            if progress_percent >= 100:
                                self.logger.info("✅ 文件上传完成")
                                return
                    except:
                        pass
            else:
                # 没有进度条，检查是否已经到达详情页面
                title_input = await page.query_selector(self.selectors['title_textbox'])
                if title_input:
                    self.logger.info("✅ 已到达视频详情页面")
                    return
            
            await page.wait_for_timeout(2000)
    
    async def _fill_video_details(self, page: Page, task: TaskModel):
        """填写视频详情"""
        self.logger.info("填写视频详情")
        
        # 填写标题
        await self._fill_title(page, task.title)
        
        # 填写描述（如果有）
        if task.description:
            await self._fill_description(page, task.description)
    
    async def _fill_title(self, page: Page, title: str):
        """填写视频标题"""
        self.logger.info(f"填写标题: {title}")
        
        try:
            # 等待标题输入框出现
            title_input = await page.wait_for_selector(self.selectors['title_textbox'], timeout=15000)
            
            # 清空现有内容
            await title_input.click()
            await page.keyboard.press('Control+a')
            await page.keyboard.press('Delete')
            
            # 输入新标题
            await title_input.type(title, delay=50)
            await page.wait_for_timeout(1000)
            
            self.logger.info("✅ 标题填写完成")
            
        except Exception as e:
            raise Exception(f"填写标题失败: {e}")
    
    async def _fill_description(self, page: Page, description: str):
        """填写视频描述"""
        self.logger.info("填写视频描述")
        
        try:
            # 查找描述输入框（可能需要滚动或点击展开）
            description_selectors = [
                '#textbox',  # 通用文本框
                'div[contenteditable="true"]',  # 可编辑div
                'textarea',  # 文本区域
                '[aria-label*="description" i]',  # 包含description的aria-label
                '[aria-label*="描述" i]'  # 中文描述
            ]
            
            description_input = None
            for selector in description_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        # 检查是否是描述输入框
                        placeholder = await element.get_attribute('placeholder') or ''
                        aria_label = await element.get_attribute('aria-label') or ''
                        
                        if ('description' in placeholder.lower() or 
                            'description' in aria_label.lower() or
                            '描述' in placeholder or '描述' in aria_label):
                            description_input = element
                            break
                    
                    if description_input:
                        break
                except:
                    continue
            
            if description_input:
                await description_input.click()
                await description_input.fill(description)
                await page.wait_for_timeout(1000)
                self.logger.info("✅ 描述填写完成")
            else:
                self.logger.warning("未找到描述输入框，跳过描述填写")
                
        except Exception as e:
            self.logger.warning(f"填写描述失败: {e}")
    
    async def _configure_video_settings(self, page: Page, task: TaskModel):
        """配置视频设置"""
        self.logger.info("配置视频设置")
        
        try:
            # 设置儿童内容选项
            await self._set_kids_content(page, task.audience_made_for_kids)
            
            # 设置年龄限制
            await self._set_age_restriction(page, task.age_restriction_18plus)
            
            # 设置语言
            await self._set_language(page, task.language)
            
            # 设置混剪选项
            await self._set_remix_settings(page, task.allow_remix)
            
        except Exception as e:
            self.logger.warning(f"配置视频设置时出错: {e}")
    
    async def _set_kids_content(self, page: Page, made_for_kids: bool):
        """设置儿童内容选项"""
        try:
            # 点击儿童内容下拉菜单
            kids_dropdown = await page.wait_for_selector(self.selectors['kids_dropdown_trigger'], timeout=5000)
            await kids_dropdown.click()
            await page.wait_for_timeout(1000)
            
            # 选择选项
            if made_for_kids:
                # 选择"是，为儿童制作"
                await page.click('text="Yes, it\'s made for kids"')
            else:
                # 选择"不是为儿童制作"
                await page.click('text="No, it\'s not made for kids"')
            
            await page.wait_for_timeout(1000)
            self.logger.info(f"✅ 儿童内容设置完成: {'是' if made_for_kids else '否'}")
            
        except Exception as e:
            self.logger.warning(f"设置儿童内容失败: {e}")
    
    async def _set_age_restriction(self, page: Page, age_restricted: bool):
        """设置年龄限制"""
        try:
            # 展开年龄限制选项
            age_expand = await page.query_selector(self.selectors['age_restriction_expand'])
            if age_expand:
                await age_expand.click()
                await page.wait_for_timeout(1000)
            
            # 设置年龄限制
            if age_restricted:
                await page.click('text="Yes, restrict my video to viewers over 18 only"')
            else:
                await page.click('text="No, don\'t restrict my video to viewers over 18 only"')
            
            await page.wait_for_timeout(1000)
            self.logger.info(f"✅ 年龄限制设置完成: {'18+' if age_restricted else '无限制'}")
            
        except Exception as e:
            self.logger.warning(f"设置年龄限制失败: {e}")
    
    async def _set_language(self, page: Page, language: str):
        """设置语言"""
        try:
            # 点击语言下拉菜单
            language_dropdown = await page.query_selector(self.selectors['language_dropdown'])
            if language_dropdown:
                await language_dropdown.click()
                await page.wait_for_timeout(1000)
                
                # 选择语言（默认英语）
                if language.lower().startswith('en'):
                    await page.click('text="English (United States)"')
                else:
                    # 尝试选择其他语言
                    await page.click(f'text="{language}"')
                
                await page.wait_for_timeout(1000)
                self.logger.info(f"✅ 语言设置完成: {language}")
            
        except Exception as e:
            self.logger.warning(f"设置语言失败: {e}")
    
    async def _set_remix_settings(self, page: Page, allow_remix: bool):
        """设置混剪选项"""
        try:
            if allow_remix:
                # 允许音频混剪
                await page.click('text="Allow only audio remixing"')
            else:
                # 不允许混剪
                remix_off = await page.query_selector('#offRadio')
                if remix_off:
                    await remix_off.click()
            
            await page.wait_for_timeout(1000)
            self.logger.info(f"✅ 混剪设置完成: {'允许' if allow_remix else '不允许'}")
            
        except Exception as e:
            self.logger.warning(f"设置混剪选项失败: {e}")
    
    async def _publish_video(self, page: Page, task: TaskModel) -> Optional[str]:
        """发布视频"""
        self.logger.info("发布视频")
        
        try:
            # 点击下一步按钮（可能需要多次）
            for step in range(3):  # 通常有3个步骤
                try:
                    next_button = await page.wait_for_selector(self.selectors['next_button'], timeout=10000)
                    await next_button.click()
                    await page.wait_for_timeout(2000)
                    self.logger.info(f"✅ 完成步骤 {step + 1}")
                except PlaywrightTimeoutError:
                    break
            
            # 设置可见性
            await self._set_visibility(page, task.visibility)
            
            # 点击发布按钮
            publish_button = await page.wait_for_selector(self.selectors['publish_button'], timeout=15000)
            await publish_button.click()
            await page.wait_for_timeout(3000)
            
            # 等待发布完成并获取视频URL
            video_url = await self._wait_for_publish_complete(page)
            
            return video_url
            
        except Exception as e:
            raise Exception(f"发布视频失败: {e}")
    
    async def _set_visibility(self, page: Page, visibility: VisibilityType):
        """设置视频可见性"""
        try:
            visibility_map = {
                VisibilityType.PUBLIC: 'text="Public"',
                VisibilityType.UNLISTED: 'text="Unlisted"', 
                VisibilityType.PRIVATE: 'text="Private"'
            }
            
            visibility_selector = visibility_map.get(visibility, 'text="Unlisted"')
            await page.click(visibility_selector)
            await page.wait_for_timeout(1000)
            
            self.logger.info(f"✅ 可见性设置完成: {visibility.value}")
            
        except Exception as e:
            self.logger.warning(f"设置可见性失败: {e}")
    
    async def _wait_for_publish_complete(self, page: Page) -> Optional[str]:
        """等待发布完成并获取视频URL"""
        self.logger.info("等待发布完成...")
        
        try:
            # 等待发布完成页面
            await page.wait_for_selector(self.selectors['done_button'], timeout=60000)
            
            # 尝试获取视频URL
            video_url = None
            
            # 方法1: 从页面URL获取
            if '/watch?v=' in page.url:
                video_url = page.url
            
            # 方法2: 从页面元素获取
            if not video_url:
                url_elements = await page.query_selector_all('a[href*="/watch?v="]')
                if url_elements:
                    href = await url_elements[0].get_attribute('href')
                    if href:
                        video_url = f"https://www.youtube.com{href}" if href.startswith('/') else href
            
            self.logger.info(f"✅ 视频发布完成: {video_url}")
            return video_url
            
        except PlaywrightTimeoutError:
            raise Exception("发布超时")
    
    def _extract_video_id(self, video_url: str) -> Optional[str]:
        """从视频URL提取视频ID"""
        if not video_url:
            return None
        
        import re
        match = re.search(r'[?&]v=([^&]+)', video_url)
        return match.group(1) if match else None
