"""
工具函数模块

提供各种通用的工具函数。
"""

import time
import random
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple
import logging


def generate_task_id() -> str:
    """
    生成唯一的任务 ID
    
    Returns:
        str: 任务 ID
    """
    return str(uuid.uuid4())


def random_delay(min_seconds: int = 2, max_seconds: int = 5):
    """
    随机延迟
    
    Args:
        min_seconds: 最小延迟秒数
        max_seconds: 最大延迟秒数
    """
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)


def format_duration(seconds: float) -> str:
    """
    格式化持续时间
    
    Args:
        seconds: 秒数
        
    Returns:
        str: 格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"


def ensure_directory(path: str) -> Path:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        path: 目录路径
        
    Returns:
        Path: 目录路径对象
    """
    dir_path = Path(path)
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path


def get_timestamp() -> str:
    """
    获取当前时间戳字符串
    
    Returns:
        str: 时间戳字符串
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")


def safe_filename(filename: str) -> str:
    """
    生成安全的文件名（移除特殊字符）
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 安全的文件名
    """
    # 移除或替换不安全的字符
    unsafe_chars = '<>:"/\\|?*'
    safe_name = filename
    for char in unsafe_chars:
        safe_name = safe_name.replace(char, '_')
    return safe_name


def parse_schedule_time(time_str: str) -> Optional[Tuple[str, str]]:
    """
    解析定时发布时间字符串
    
    Args:
        time_str: 时间字符串，格式如 "2025-08-20 14:30:00"
        
    Returns:
        Optional[Tuple[str, str]]: (日期, 时间) 元组，解析失败返回 None
    """
    try:
        dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        date_str = dt.strftime("%Y-%m-%d")
        time_str = dt.strftime("%H:%M")
        return date_str, time_str
    except ValueError:
        return None


def log_step(logger: logging.Logger, step: str, start_time: Optional[float] = None):
    """
    记录步骤日志
    
    Args:
        logger: 日志记录器
        step: 步骤描述
        start_time: 开始时间（用于计算耗时）
    """
    current_time = time.time()
    if start_time:
        duration = current_time - start_time
        logger.info(f"✓ {step} (耗时: {format_duration(duration)})")
    else:
        logger.info(f"→ {step}")
    return current_time
