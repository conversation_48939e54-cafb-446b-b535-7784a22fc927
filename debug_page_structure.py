#!/usr/bin/env python3
"""
快速页面结构调试工具
实时分析YouTube页面组成和元素状态
"""

import sys
import asyncio
import json
from pathlib import Path
from playwright.async_api import async_playwright

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

async def analyze_page_structure():
    """分析页面结构"""
    print("🔍 启动页面结构分析工具...")
    
    try:
        # 连接到已打开的比特浏览器
        from youtube_uploader.executor import TaskExecutor
        from youtube_uploader.excel_handler import ExcelHandler
        
        # 读取任务
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        task = tasks[0]
        
        # 初始化执行器
        config = {'max_retries': 3}
        executor = TaskExecutor(config)
        
        # 打开浏览器
        print(f"📱 打开浏览器 profile: {task.profile_name}")
        ws_address = executor.bit_browser_manager.open_browser(task.profile_name)
        
        if not ws_address:
            print("❌ 无法打开浏览器")
            return
            
        print(f"✅ 浏览器已连接: {ws_address}")
        
        # 连接到浏览器
        async with async_playwright() as p:
            browser = await p.chromium.connect_over_cdp(ws_address)
            context = browser.contexts[0] if browser.contexts else await browser.new_context()
            page = await context.new_page()
            
            print("\n🌐 导航到YouTube上传页面...")
            await page.goto('https://www.youtube.com/upload', wait_until='networkidle')
            
            print("\n📊 分析页面结构...")
            
            # 1. 获取页面基本信息
            title = await page.title()
            url = page.url
            print(f"页面标题: {title}")
            print(f"当前URL: {url}")
            
            # 2. 分析页面元素
            print("\n🔍 扫描关键元素...")
            
            # 检查常见的上传相关元素
            selectors_to_check = [
                # 文件上传
                'input[type="file"]',
                '[aria-label*="upload" i]',
                '[aria-label*="选择文件" i]',
                'ytcp-uploads-file-picker',
                
                # 标题输入
                '[aria-label*="title" i]',
                '[aria-label*="标题" i]',
                'div[contenteditable="true"]',
                'textarea',
                'input[type="text"]',
                
                # 描述输入
                '[aria-label*="description" i]',
                '[aria-label*="描述" i]',
                
                # 发布按钮
                '[aria-label*="publish" i]',
                '[aria-label*="发布" i]',
                'button[contains(text(), "发布")]',
                'button[contains(text(), "Publish")]',
                
                # 进度指示器
                '[role="progressbar"]',
                '.progress',
                '[aria-label*="progress" i]',
                
                # 错误信息
                '[role="alert"]',
                '.error',
                '[aria-label*="error" i]',
                
                # 加载指示器
                '[aria-label*="loading" i]',
                '.loading',
                '.spinner'
            ]
            
            found_elements = {}
            for selector in selectors_to_check:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        found_elements[selector] = len(elements)
                        print(f"✅ 找到 {len(elements)} 个元素: {selector}")
                        
                        # 获取第一个元素的详细信息
                        first_element = elements[0]
                        tag_name = await first_element.evaluate('el => el.tagName')
                        is_visible = await first_element.is_visible()
                        text_content = await first_element.text_content()
                        
                        print(f"   - 标签: {tag_name}")
                        print(f"   - 可见: {is_visible}")
                        if text_content and len(text_content.strip()) > 0:
                            print(f"   - 文本: {text_content.strip()[:50]}...")
                            
                except Exception as e:
                    pass
            
            # 3. 检查页面状态
            print(f"\n📈 页面状态检查...")
            
            # 检查是否有加载中的元素
            loading_elements = await page.query_selector_all('[aria-label*="loading" i], .loading, .spinner')
            if loading_elements:
                print(f"⏳ 发现 {len(loading_elements)} 个加载指示器")
                for i, elem in enumerate(loading_elements[:3]):  # 只显示前3个
                    text = await elem.text_content()
                    is_visible = await elem.is_visible()
                    print(f"   {i+1}. 可见: {is_visible}, 文本: {text}")
            
            # 检查网络请求
            print(f"\n🌐 监控网络活动...")
            
            # 等待一段时间并监控网络请求
            network_requests = []
            
            def handle_request(request):
                if 'youtube.com' in request.url:
                    network_requests.append({
                        'url': request.url,
                        'method': request.method,
                        'resource_type': request.resource_type
                    })
            
            page.on('request', handle_request)
            
            # 等待5秒钟监控网络活动
            print("等待5秒钟监控网络活动...")
            await asyncio.sleep(5)
            
            print(f"📡 捕获到 {len(network_requests)} 个网络请求")
            for req in network_requests[-5:]:  # 显示最后5个请求
                print(f"   - {req['method']} {req['resource_type']}: {req['url'][:80]}...")
            
            # 4. 生成页面快照
            print(f"\n📸 生成页面快照...")
            
            # 截图
            await page.screenshot(path='debug_page_screenshot.png', full_page=True)
            print("✅ 页面截图已保存: debug_page_screenshot.png")
            
            # 保存页面HTML
            html_content = await page.content()
            with open('debug_page_content.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print("✅ 页面HTML已保存: debug_page_content.html")
            
            # 保存元素信息
            with open('debug_elements_found.json', 'w', encoding='utf-8') as f:
                json.dump(found_elements, f, indent=2, ensure_ascii=False)
            print("✅ 元素信息已保存: debug_elements_found.json")
            
            print(f"\n🎉 页面分析完成！")
            print(f"📁 生成的文件:")
            print(f"   - debug_page_screenshot.png (页面截图)")
            print(f"   - debug_page_content.html (页面HTML源码)")
            print(f"   - debug_elements_found.json (找到的元素)")
            
            await page.close()
            await browser.close()
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 YouTube页面结构快速分析工具")
    print("=" * 60)
    
    asyncio.run(analyze_page_structure())
