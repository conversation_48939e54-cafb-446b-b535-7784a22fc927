{"timestamp": "2025-08-14T10:55:45.153702", "page_url": "https://studio.youtube.com/channel/UCC_9D6Uj7KDPmjvtwSSQDJA", "total_elements": 113, "detailed_selectors": {"visibility_options": {}, "audience_options": {"audience_other": [{"index": 80, "tag": "div", "type": null, "aria_label": null, "class": "radio-button-title style-scope ytkc-made-for-kids-select", "id": null, "role": null, "value": null, "text": "\n  Is this video made for kids? (required)\n  ", "parent_text": "", "selectors": [".radio-button-title", "text=\"Is this video made for kids? (required)\""]}, {"index": 81, "tag": "a", "type": null, "aria_label": "Learn more about video's audience settings", "class": "style-scope ytkc-made-for-kids-select", "id": null, "role": null, "value": null, "text": "\n        Learn more\n      ", "parent_text": "Features like personalized ads and notifications won’t be available on videos made for kids.\n  Videos that are set as made for kids by you are more likely to be recommended alongside other\n  kids’ vid", "selectors": ["[aria-label=\"Learn more about video's audience settings\"]", ".style-scope", "text=\"Learn more\""]}, {"index": 83, "tag": "div", "type": null, "aria_label": null, "class": "style-scope tp-yt-paper-radio-button", "id": "radioContainer", "role": null, "value": null, "text": "\n  \n  \n\n\n", "parent_text": "Yes, it's made for kids.\n          Features like personalized ads and notifications won’t be available on videos made for kids.\n  Videos that are set as made for kids by you are more likely to be reco", "selectors": ["#radioContainer", ".style-scope", "text=\"\""]}, {"index": 89, "tag": "div", "type": null, "aria_label": null, "class": "style-scope tp-yt-paper-radio-button", "id": "radioContainer", "role": null, "value": null, "text": "\n  \n  \n", "parent_text": "No, it's not made for kids", "selectors": ["#radioContainer", ".style-scope", "text=\"\""]}], "made_for_kids_yes": [{"index": 82, "tag": "tp-yt-paper-radio-button", "type": null, "aria_label": null, "class": "style-scope ytkc-made-for-kids-select", "id": null, "role": "radio", "value": null, "text": "\n  \n  \n\n\n\n        Yes, it's made for kids.\n          Features like personalized ads and notifications won’t be available on videos made for kids.\n  Videos that are set as made for kids by you are more likely to be recommended alongside other\n  kids’ videos.\n          \n            Learn more\n          \n", "parent_text": "Yes, it's made for kids.\n          Features like personalized ads and notifications won’t be available on videos made for kids.\n  Videos that are set as made for kids by you are more likely to be reco", "selectors": [".style-scope"]}, {"index": 87, "tag": "div", "type": null, "aria_label": null, "class": "style-scope tp-yt-paper-radio-button", "id": "radioLabel", "role": null, "value": null, "text": "\n        Yes, it's made for kids.\n          Features like personalized ads and notifications won’t be available on videos made for kids.\n  Videos that are set as made for kids by you are more likely to be recommended alongside other\n  kids’ videos.\n          \n            Learn more\n          ", "parent_text": "Yes, it's made for kids.\n          Features like personalized ads and notifications won’t be available on videos made for kids.\n  Videos that are set as made for kids by you are more likely to be reco", "selectors": ["#radioLabel", ".style-scope"]}], "made_for_kids_no": [{"index": 88, "tag": "tp-yt-paper-radio-button", "type": null, "aria_label": null, "class": "style-scope ytkc-made-for-kids-select", "id": null, "role": "radio", "value": null, "text": "\n  \n  \n\n        No, it's not made for kids\n      \n", "parent_text": "Yes, it's made for kids.\n          Features like personalized ads and notifications won’t be available on videos made for kids.\n  Videos that are set as made for kids by you are more likely to be reco", "selectors": [".style-scope", "text=\"No, it's not made for kids\""]}, {"index": 92, "tag": "div", "type": null, "aria_label": null, "class": "style-scope tp-yt-paper-radio-button", "id": "radioLabel", "role": null, "value": null, "text": "\n        No, it's not made for kids\n      ", "parent_text": "No, it's not made for kids", "selectors": ["#radioLabel", ".style-scope", "text=\"No, it's not made for kids\""]}]}, "age_restriction_options": {"restriction_other": [{"index": 94, "tag": "button", "type": null, "aria_label": null, "class": "expand-button remove-default-style style-scope ytcp-video-metadata-editor-basics", "id": null, "role": null, "value": null, "text": "\n\nAge restriction (advanced)", "parent_text": "", "selectors": [".expand-button", "text=\"Age restriction (advanced)\""]}]}, "remix_options": {"remix_other": [{"index": 1, "tag": "ytcp-icon-button", "type": null, "aria_label": "Collapse menu", "class": "rtl-flip style-scope ytcp-header", "id": "collapse-expand-icon", "role": "button", "value": null, "text": "\n", "parent_text": "Skip navigation\n\n\n\n\n  \n\n  \n    \n      \n    \n  \n\n  \n\n  \n  \n  \n\n\n  \n    \n      \n        \n          \n             Your recent videos \n            \n              \n                \n                  Show a", "selectors": ["#collapse-expand-icon", "[aria-label=\"Collapse menu\"]", ".rtl-flip", "text=\"\""]}, {"index": 21, "tag": "button", "type": null, "aria_label": "Go to video analytics", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Go to video analytics", "parent_text": "Go to video analytics", "selectors": ["[aria-label=\"Go to video analytics\"]", ".ytcpButtonShapeImplHost", "text=\"Go to video analytics\""]}, {"index": 43, "tag": "button", "type": null, "aria_label": "Go to videos", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Go to videos", "parent_text": "Go to videos", "selectors": ["[aria-label=\"Go to videos\"]", ".ytcpButtonShapeImplHost", "text=\"Go to videos\""]}, {"index": 63, "tag": "button", "type": null, "aria_label": null, "class": "step remove-default-style style-scope ytcp-stepper", "id": "step-badge-1", "role": "tab", "value": null, "text": "Video elements\n\n", "parent_text": "Video elements", "selectors": ["#step-badge-1", ".step", "text=\"Video elements\""]}, {"index": 77, "tag": "a", "type": null, "aria_label": "Learn more about playlists", "class": "style-scope ytcp-video-metadata-editor-basics", "id": null, "role": null, "value": null, "text": "\n      Learn more\n    ", "parent_text": "Add your video to one or more playlists to organize your content for viewers.\n    \n      Learn more", "selectors": ["[aria-label=\"Learn more about playlists\"]", ".style-scope", "text=\"Learn more\""]}]}, "altered_content_options": {}, "other_options": {"other_2": [{"index": 2, "tag": "button", "type": null, "aria_label": "Skip navigation", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Skip navigation", "parent_text": "Skip navigation", "selectors": ["[aria-label=\"Skip navigation\"]", ".ytcpButtonShapeImplHost", "text=\"Skip navigation\""]}], "other_9": [{"index": 9, "tag": "button", "type": null, "aria_label": "Create", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--outline ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--icon-leading ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Create", "parent_text": "Create", "selectors": ["[aria-label=\"Create\"]", ".ytcpButtonShapeImplHost", "text=\"Create\""]}], "other_14": [{"index": 14, "tag": "h2", "type": null, "aria_label": "Latest YouTube Short performance", "class": "title apply-item-padding style-scope ytcd-entity-snapshot-item", "id": "entity-snapshot-title", "role": null, "value": null, "text": "\n      Latest YouTube Short performance\n    ", "parent_text": "", "selectors": ["#entity-snapshot-title", "[aria-label=\"Latest YouTube Short performance\"]", ".title", "text=\"Latest YouTube Short performance\""]}], "other_22": [{"index": 22, "tag": "button", "type": null, "aria_label": "See comments (0)", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "See comments (0)", "parent_text": "See comments (0)", "selectors": ["[aria-label=\"See comments (0)\"]", ".ytcpButtonShapeImplHost", "text=\"See comments (0)\""]}], "other_44": [{"index": 44, "tag": "button", "type": null, "aria_label": "Create post", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--filled ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Create post", "parent_text": "Create post", "selectors": ["[aria-label=\"Create post\"]", ".ytcpButtonShapeImplHost", "text=\"Create post\""]}], "other_45": [{"index": 45, "tag": "button", "type": null, "aria_label": "Go to channel analytics", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Go to channel analytics", "parent_text": "Go to channel analytics", "selectors": ["[aria-label=\"Go to channel analytics\"]", ".ytcpButtonShapeImplHost", "text=\"Go to channel analytics\""]}], "other_46": [{"index": 46, "tag": "button", "type": null, "aria_label": "View more", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "View more", "parent_text": "View more", "selectors": ["[aria-label=\"View more\"]", ".ytcpButtonShapeImplHost", "text=\"View more\""]}], "other_47": [{"index": 47, "tag": "button", "type": null, "aria_label": "See all", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "See all", "parent_text": "See all", "selectors": ["[aria-label=\"See all\"]", ".ytcpButtonShapeImplHost", "text=\"See all\""]}], "other_51": [{"index": 51, "tag": "button", "type": null, "aria_label": "Get started", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Get started", "parent_text": "Get started", "selectors": ["[aria-label=\"Get started\"]", ".ytcpButtonShapeImplHost", "text=\"Get started\""]}], "other_55": [{"index": 55, "tag": "button", "type": null, "aria_label": "Check it out", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Check it out", "parent_text": "Check it out", "selectors": ["[aria-label=\"Check it out\"]", ".ytcpButtonShapeImplHost", "text=\"Check it out\""]}], "other_58": [{"index": 58, "tag": "button", "type": null, "aria_label": "Watch on YouTube", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Watch on YouTube", "parent_text": "Watch on YouTube", "selectors": ["[aria-label=\"Watch on YouTube\"]", ".ytcpButtonShapeImplHost", "text=\"Watch on YouTube\""]}], "other_62": [{"index": 62, "tag": "button", "type": null, "aria_label": null, "class": "step remove-default-style style-scope ytcp-stepper", "id": "step-badge-0", "role": "tab", "value": null, "text": "Details\n\n", "parent_text": "Details", "selectors": ["#step-badge-0", ".step", "text=\"Details\""]}], "other_64": [{"index": 64, "tag": "button", "type": null, "aria_label": null, "class": "step remove-default-style style-scope ytcp-stepper", "id": "step-badge-2", "role": "tab", "value": null, "text": "Checks\n\n", "parent_text": "Checks", "selectors": ["#step-badge-2", ".step", "text=\"Checks\""]}], "other_65": [{"index": 65, "tag": "button", "type": null, "aria_label": null, "class": "step remove-default-style style-scope ytcp-stepper", "id": "step-badge-3", "role": "tab", "value": null, "text": "Visibility\n\n", "parent_text": "Visibility", "selectors": ["#step-badge-3", ".step", "text=\"Visibility\""]}], "other_66": [{"index": 66, "tag": "button", "type": null, "aria_label": "Reuse details", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Reuse details", "parent_text": "Reuse details", "selectors": ["[aria-label=\"Reuse details\"]", ".ytcpButtonShapeImplHost", "text=\"Reuse details\""]}], "other_67": [{"index": 67, "tag": "div", "type": null, "aria_label": "Add a title that describes your video (type @ to mention a channel)", "class": "style-scope ytcp-social-suggestions-textbox", "id": "textbox", "role": "textbox", "value": null, "text": "深度分析测试", "parent_text": "深度分析测试", "selectors": ["#textbox", "[aria-label=\"Add a title that describes your video (type @ to mention a channel)\"]", ".style-scope", "text=\"深度分析测试\""]}], "other_76": [{"index": 76, "tag": "div", "type": null, "aria_label": "Tell viewers about your video (type @ to mention a channel)", "class": "style-scope ytcp-social-suggestions-textbox", "id": "textbox", "role": "textbox", "value": null, "text": "深度分析测试描述", "parent_text": "深度分析测试描述", "selectors": ["#textbox", "[aria-label=\"Tell viewers about your video (type @ to mention a channel)\"]", ".style-scope", "text=\"深度分析测试描述\""]}], "other_78": [{"index": 78, "tag": "ytcp-video-metadata-playlists", "type": null, "aria_label": "Add to playlist", "class": "style-scope ytcp-video-metadata-editor-basics", "id": null, "role": null, "value": null, "text": "\n\n\n  \n  \n    \n  \n  \n  \n    \n    \n    Select\n  \n  \n  \n    \n      \n      \n        \n\n      \n    \n  \n\n  \n    \n\n\n  \n\n\n", "parent_text": "Select", "selectors": ["[aria-label=\"Add to playlist\"]", ".style-scope", "text=\"Select\""]}], "other_79": [{"index": 79, "tag": "ytcp-dropdown-trigger", "type": null, "aria_label": "Select playlists", "class": "use-placeholder style-scope ytcp-text-dropdown-trigger style-scope ytcp-text-dropdown-trigger", "id": null, "role": "button", "value": null, "text": "\n\n\n  \n  \n    \n  \n  \n  \n    \n    \n    Select\n  \n  \n  \n    \n      \n      \n        \n\n      \n    \n  \n\n  \n    \n\n\n  \n\n\n", "parent_text": "Select", "selectors": ["[aria-label=\"Select playlists\"]", ".use-placeholder", "text=\"Select\""]}], "other_107": [{"index": 107, "tag": "button", "type": null, "aria_label": "Show advanced settings", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--tonal ytcpButtonShapeImpl--mono ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Show more", "parent_text": "Show more", "selectors": ["[aria-label=\"Show advanced settings\"]", ".ytcpButtonShapeImplHost", "text=\"Show more\""]}], "other_111": [{"index": 111, "tag": "button", "type": null, "aria_label": "Next", "class": "ytcpButtonShapeImplHost ytcpButtonShapeImpl--filled ytcpButtonShapeImpl--size-m ytcpButtonShapeImpl--disabled ytcpButtonShapeImpl--enable-backdrop-filter-experiment", "id": null, "role": null, "value": null, "text": "Next", "parent_text": "Next", "selectors": ["[aria-label=\"Next\"]", ".ytcpButtonShapeImplHost", "text=\"Next\""]}]}}}