"""
YouTube 自动化核心逻辑

基于 MCP 分析结果，实现 YouTube 视频上传、信息填写、参数设置、发布等核心自动化流程。
"""

import asyncio
import time
import json
from typing import Optional, Dict, Any, Callable, List, Tuple
from pathlib import Path
from datetime import datetime, timedelta
from enum import Enum
import logging
from dataclasses import dataclass, field

from playwright.async_api import Page, Browser, TimeoutError as PlaywrightTimeoutError
from .models import TaskModel, ResultModel, TaskStatus, ProgressModel
from .selectors import YouTubeSelectors
from .utils import random_delay, log_step, generate_task_id


# 上传阶段枚举
class UploadStage(str, Enum):
    """上传阶段"""
    INITIALIZING = "initializing"
    NAVIGATING = "navigating"
    UPLOADING_FILE = "uploading_file"
    PROCESSING = "processing"
    FILLING_INFO = "filling_info"
    SETTING_OPTIONS = "setting_options"
    HANDLING_YPP = "handling_ypp"
    SETTING_VISIBILITY = "setting_visibility"
    PUBLISHING = "publishing"
    COMPLETED = "completed"
    FAILED = "failed"


# 上传状态数据类
@dataclass
class UploadProgress:
    """上传进度信息"""
    stage: UploadStage = UploadStage.INITIALIZING
    progress_percent: float = 0.0
    current_step: str = ""
    total_steps: int = 8
    completed_steps: int = 0
    start_time: datetime = field(default_factory=datetime.now)
    last_update: datetime = field(default_factory=datetime.now)
    error_count: int = 0
    retry_count: int = 0

    def update(self, stage: UploadStage = None, progress: float = None,
               step: str = None, completed_steps: int = None):
        """更新进度信息"""
        if stage is not None:
            self.stage = stage
        if progress is not None:
            self.progress_percent = progress
        if step is not None:
            self.current_step = step
        if completed_steps is not None:
            self.completed_steps = completed_steps
            self.progress_percent = (completed_steps / self.total_steps) * 100
        self.last_update = datetime.now()

    def get_elapsed_time(self) -> timedelta:
        """获取已用时间"""
        return datetime.now() - self.start_time

    def estimate_remaining_time(self) -> Optional[timedelta]:
        """估算剩余时间"""
        if self.progress_percent <= 0:
            return None
        elapsed = self.get_elapsed_time()
        total_estimated = elapsed / (self.progress_percent / 100)
        return total_estimated - elapsed


# 上传异常类
class YouTubeUploadError(Exception):
    """YouTube上传错误基类"""
    pass


class NavigationError(YouTubeUploadError):
    """导航错误"""
    pass


class FileUploadError(YouTubeUploadError):
    """文件上传错误"""
    pass


class FormFillError(YouTubeUploadError):
    """表单填写错误"""
    pass


class PublishError(YouTubeUploadError):
    """发布错误"""
    pass


class YouTubeAutomation:
    """YouTube 自动化操作类"""

    def __init__(self, config: Dict[str, Any], progress_callback: Optional[Callable[[UploadProgress], None]] = None):
        """
        初始化 YouTube 自动化操作

        Args:
            config: 配置字典
            progress_callback: 进度回调函数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        self.selectors = YouTubeSelectors()
        self.progress_callback = progress_callback

        # 配置参数
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 5)
        self.page_load_timeout = config.get('page_load_timeout', 30) * 1000
        self.element_timeout = config.get('element_timeout', 10) * 1000
        self.upload_timeout = config.get('upload_timeout', 300) * 1000  # 5分钟

        # 状态管理
        self._current_progress: Optional[UploadProgress] = None
        self._upload_start_time: Optional[datetime] = None
    
    async def upload_video(self, page: Page, task: TaskModel) -> ResultModel:
        """
        执行单个视频上传任务

        Args:
            page: Playwright 页面对象
            task: 任务模型

        Returns:
            ResultModel: 执行结果
        """
        task_id = generate_task_id()
        self._upload_start_time = datetime.now()

        # 初始化进度跟踪
        self._current_progress = UploadProgress()
        self._update_progress(UploadStage.INITIALIZING, 0, "初始化上传任务", 0)

        result = ResultModel(
            task_id=task_id,
            profile_name=task.profile_name,
            video_path=task.video_path,
            status=TaskStatus.RUNNING,
            started_at=self._upload_start_time
        )

        try:
            self.logger.info(f"开始上传任务: {task.profile_name} - {task.title}")

            # 执行上传流程
            await self._execute_upload_workflow(page, task, result)

            # 更新最终结果
            if result.status == TaskStatus.RUNNING:
                result.status = TaskStatus.SUCCESS
                self._update_progress(UploadStage.COMPLETED, 100, "上传完成", 8)

            result.finished_at = datetime.now()
            result.calculate_duration()

            self.logger.info(f"任务完成: {task.profile_name} - {task.title}")
            return result

        except Exception as e:
            self.logger.error(f"任务失败: {task.profile_name} - {str(e)}")

            # 保存错误截图和诊断信息
            screenshot_path = await self._save_error_screenshot(page, task_id)
            diagnostic_info = await self._collect_diagnostic_info(page, task, e)

            result.status = TaskStatus.FAILED
            result.error_message = str(e)
            result.screenshot_path = screenshot_path
            result.diagnostic_info = diagnostic_info
            result.finished_at = datetime.now()
            result.calculate_duration()

            self._update_progress(UploadStage.FAILED, 0, f"上传失败: {str(e)}")

            return result

    async def _execute_upload_workflow(self, page: Page, task: TaskModel, result: ResultModel):
        """执行上传工作流"""
        workflow_steps = [
            (self._navigate_to_upload_page, "导航到上传页面", UploadStage.NAVIGATING),
            (lambda p: self._upload_video_file(p, task.video_path), "上传视频文件", UploadStage.UPLOADING_FILE),
            (lambda p: self._wait_for_processing(p), "等待视频处理", UploadStage.PROCESSING),
            (lambda p: self._fill_basic_info(p, task), "填写基本信息", UploadStage.FILLING_INFO),
            (lambda p: self._set_advanced_options(p, task), "设置高级选项", UploadStage.SETTING_OPTIONS),
            (lambda p: self._handle_ypp_section(p), "处理YPP部分", UploadStage.HANDLING_YPP),
            (lambda p: self._set_visibility(p, task), "设置可见性", UploadStage.SETTING_VISIBILITY),
            (lambda p: self._publish_video(p, result), "发布视频", UploadStage.PUBLISHING),
        ]

        for step_index, (step_func, step_name, stage) in enumerate(workflow_steps):
            try:
                self._update_progress(stage, None, step_name, step_index)
                await self._execute_step_with_retry(step_func, page, step_name)
                self._update_progress(stage, None, f"{step_name}完成", step_index + 1)

            except Exception as e:
                self.logger.error(f"步骤失败: {step_name} - {e}")
                raise YouTubeUploadError(f"在'{step_name}'步骤失败: {e}") from e

    async def _execute_step_with_retry(self, step_func: Callable, page: Page, step_name: str):
        """带重试的步骤执行"""
        last_exception = None

        for attempt in range(self.max_retries):
            try:
                if self._current_progress:
                    self._current_progress.retry_count = attempt

                await step_func(page)
                return  # 成功执行

            except Exception as e:
                last_exception = e
                self.logger.warning(f"{step_name} 尝试 {attempt + 1}/{self.max_retries} 失败: {e}")

                if self._current_progress:
                    self._current_progress.error_count += 1

                if attempt < self.max_retries - 1:
                    # 等待后重试
                    await asyncio.sleep(self.retry_delay)
                    self._update_progress(None, None, f"{step_name} 重试中...")

        # 所有重试都失败
        raise last_exception

    def _update_progress(self, stage: UploadStage = None, progress: float = None,
                        step: str = None, completed_steps: int = None):
        """更新进度并调用回调"""
        if self._current_progress:
            self._current_progress.update(stage, progress, step, completed_steps)

            if self.progress_callback:
                try:
                    self.progress_callback(self._current_progress)
                except Exception as e:
                    self.logger.warning(f"进度回调失败: {e}")
    
    async def _navigate_to_upload_page(self, page: Page):
        """导航到上传页面"""
        try:
            self.logger.info("导航到 YouTube Studio")

            # 访问 YouTube Studio
            await page.goto("https://studio.youtube.com", timeout=self.page_load_timeout)

            # 等待页面加载
            await page.wait_for_load_state('networkidle', timeout=self.page_load_timeout)

            # 检查是否成功加载
            await self._verify_youtube_studio_loaded(page)

            self.logger.info("成功导航到 YouTube Studio")

            # 随机延迟
            await self._random_delay()

        except Exception as e:
            raise NavigationError(f"导航到YouTube Studio失败: {e}") from e

    async def _verify_youtube_studio_loaded(self, page: Page):
        """验证YouTube Studio是否正确加载"""
        try:
            # 检查页面标题
            title = await page.title()
            if "YouTube Studio" not in title:
                raise NavigationError(f"页面标题不正确: {title}")

            # 检查关键元素是否存在
            create_button_selectors = self.selectors.get_selector("upload", "create_button")
            found = False
            for selector in create_button_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    found = True
                    break
                except:
                    continue

            if not found:
                raise NavigationError("未找到创建按钮，可能未正确加载YouTube Studio")

        except Exception as e:
            raise NavigationError(f"验证YouTube Studio加载失败: {e}") from e
    
    async def _upload_video_file(self, page: Page, video_path: str):
        """上传视频文件"""
        try:
            self.logger.info(f"开始上传视频文件: {Path(video_path).name}")

            # 验证文件存在
            if not Path(video_path).exists():
                raise FileUploadError(f"视频文件不存在: {video_path}")

            # 获取文件大小用于进度监控
            file_size = Path(video_path).stat().st_size
            self.logger.info(f"文件大小: {file_size / (1024*1024):.1f} MB")

            # 点击创建按钮
            await self._click_element_with_selectors(
                page,
                self.selectors.get_selector("upload", "create_button"),
                "创建按钮"
            )

            await self._random_delay(1, 2)

            # 点击上传视频选项
            await self._click_element_with_selectors(
                page,
                self.selectors.get_selector("upload", "upload_video_option"),
                "上传视频选项"
            )

            await self._random_delay(1, 2)

            # 上传文件
            await self._upload_file_with_selectors(
                page,
                self.selectors.get_selector("upload", "file_input"),
                video_path
            )

            self.logger.info("视频文件上传开始")

        except Exception as e:
            raise FileUploadError(f"上传视频文件失败: {e}") from e

    async def _wait_for_processing(self, page: Page):
        """等待视频处理完成"""
        try:
            self.logger.info("等待视频处理完成")

            # 等待上传进度条出现
            progress_selectors = self.selectors.get_selector("upload", "upload_progress")
            progress_element = None

            for selector in progress_selectors:
                try:
                    progress_element = await page.wait_for_selector(selector, timeout=30000)
                    break
                except:
                    continue

            if progress_element:
                # 监控上传进度
                await self._monitor_upload_progress(page, progress_element)

            # 等待处理完成标志
            await self._wait_for_processing_complete(page)

            self.logger.info("视频处理完成")

        except Exception as e:
            raise FileUploadError(f"等待视频处理失败: {e}") from e

    async def _monitor_upload_progress(self, page: Page, progress_element):
        """监控上传进度"""
        try:
            last_progress = 0
            stall_count = 0
            max_stall_count = 30  # 30次检查无进度则认为卡住

            while True:
                try:
                    # 获取进度文本
                    progress_text = await progress_element.text_content()

                    if progress_text and "%" in progress_text:
                        # 提取进度百分比
                        import re
                        match = re.search(r'(\d+)%', progress_text)
                        if match:
                            current_progress = int(match.group(1))

                            if current_progress > last_progress:
                                last_progress = current_progress
                                stall_count = 0
                                self._update_progress(
                                    UploadStage.UPLOADING_FILE,
                                    current_progress * 0.5,  # 上传占总进度的50%
                                    f"上传进度: {current_progress}%"
                                )
                            else:
                                stall_count += 1

                            if current_progress >= 100:
                                break

                    if stall_count >= max_stall_count:
                        self.logger.warning("上传进度长时间无变化，可能出现问题")
                        break

                    await asyncio.sleep(2)  # 每2秒检查一次

                except Exception as e:
                    self.logger.warning(f"监控上传进度时出错: {e}")
                    break

        except Exception as e:
            self.logger.warning(f"上传进度监控失败: {e}")

    async def _wait_for_processing_complete(self, page: Page):
        """等待处理完成"""
        try:
            # 等待"详细信息"或类似的下一步元素出现
            details_selectors = self.selectors.get_selector("upload", "details_section")

            for selector in details_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=self.upload_timeout)
                    return
                except:
                    continue

            # 如果没有找到详细信息部分，尝试等待标题输入框
            title_selectors = self.selectors.get_selector("upload", "title_input")
            for selector in title_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=10000)
                    return
                except:
                    continue

            raise FileUploadError("无法确认视频处理完成")

        except Exception as e:
            raise FileUploadError(f"等待处理完成失败: {e}") from e

    async def _click_element_with_selectors(self, page: Page, selectors: List[str], element_name: str):
        """使用多个选择器尝试点击元素"""
        for selector in selectors:
            try:
                await page.click(selector, timeout=self.element_timeout)
                self.logger.debug(f"成功点击{element_name}: {selector}")
                return
            except Exception as e:
                self.logger.debug(f"点击{element_name}失败 ({selector}): {e}")
                continue

        raise FileUploadError(f"无法找到或点击{element_name}")

    async def _upload_file_with_selectors(self, page: Page, selectors: List[str], file_path: str):
        """使用多个选择器尝试上传文件"""
        for selector in selectors:
            try:
                await page.set_input_files(selector, file_path)
                self.logger.debug(f"成功上传文件: {selector}")
                return
            except Exception as e:
                self.logger.debug(f"上传文件失败 ({selector}): {e}")
                continue

        raise FileUploadError("无法找到文件上传输入框")

    async def _fill_element_with_selectors(self, page: Page, selectors: List[str],
                                         value: str, element_name: str, clear_first: bool = True):
        """使用多个选择器尝试填写元素"""
        for selector in selectors:
            try:
                if clear_first:
                    await page.fill(selector, "")  # 先清空
                await page.fill(selector, value)
                self.logger.debug(f"成功填写{element_name}: {selector}")
                return
            except Exception as e:
                self.logger.debug(f"填写{element_name}失败 ({selector}): {e}")
                continue

        raise FormFillError(f"无法找到或填写{element_name}")

    async def _random_delay(self, min_seconds: float = None, max_seconds: float = None):
        """异步随机延迟"""
        min_delay = min_seconds or self.config.get('random_delay_min', 1)
        max_delay = max_seconds or self.config.get('random_delay_max', 3)

        import random
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    async def _fill_basic_info(self, page: Page, task: TaskModel):
        """填写基本信息 (修复版本)"""
        try:
            self.logger.info(f"开始填写基本信息 - 标题: {task.title}")

            # 检测并清除任何错误提示
            await self._check_and_clear_error_messages(page)

            # 填写标题 (强制填写)
            title_filled = await self._fill_element_with_selectors_enhanced(
                page,
                self.selectors.get_selector("upload", "title_input"),
                task.title,
                "标题",
                required=True
            )

            if not title_filled:
                raise FormFillError(f"无法填写标题: {task.title}")

            await self._random_delay(0.5, 1.0)

            # 填写描述（如果有）
            if task.description:
                description_filled = await self._fill_element_with_selectors_enhanced(
                    page,
                    self.selectors.get_selector("upload", "description_input"),
                    task.description,
                    "描述",
                    required=False
                )

                if description_filled:
                    self.logger.info(f"描述填写成功: {task.description[:50]}...")
                else:
                    self.logger.warning("描述填写失败，但继续执行")

                await self._random_delay(0.5, 1.0)

            # 设置缩略图（如果有）
            if task.thumbnail_path and Path(task.thumbnail_path).exists():
                await self._upload_thumbnail(page, task.thumbnail_path)

            # 添加标签（如果有）
            if task.tags:
                await self._add_tags(page, task.get_tags_list())

            # 选择播放列表（如果有）
            if hasattr(task, 'playlist') and task.playlist:
                await self._select_playlist(page, task.playlist)

            self.logger.info("基本信息填写完成")

        except Exception as e:
            # 检测错误提示
            await self._check_and_clear_error_messages(page)
            raise FormFillError(f"填写基本信息失败: {e}") from e

    async def _upload_thumbnail(self, page: Page, thumbnail_path: str):
        """上传缩略图"""
        try:
            self.logger.info(f"上传缩略图: {Path(thumbnail_path).name}")

            thumbnail_selectors = self.selectors.get_selector("upload", "thumbnail_input")
            await self._upload_file_with_selectors(page, thumbnail_selectors, thumbnail_path)

            # 等待缩略图上传完成
            await asyncio.sleep(2)

        except Exception as e:
            self.logger.warning(f"上传缩略图失败: {e}")
            # 缩略图上传失败不应该中断整个流程

    async def _add_tags(self, page: Page, tags: List[str]):
        """添加标签"""
        try:
            self.logger.info(f"添加标签: {', '.join(tags[:5])}{'...' if len(tags) > 5 else ''}")

            # 限制标签数量
            limited_tags = tags[:10]  # YouTube最多支持15个标签，我们保守一点

            tags_selectors = self.selectors.get_selector("upload", "tags_input")

            for selector in tags_selectors:
                try:
                    # 清空现有标签
                    await page.fill(selector, "")

                    # 添加标签，用逗号分隔
                    tags_text = ", ".join(limited_tags)
                    await page.fill(selector, tags_text)

                    # 按回车确认
                    await page.press(selector, "Enter")

                    self.logger.debug(f"成功添加标签: {selector}")
                    return

                except Exception as e:
                    self.logger.debug(f"添加标签失败 ({selector}): {e}")
                    continue

            self.logger.warning("无法找到标签输入框")

        except Exception as e:
            self.logger.warning(f"添加标签失败: {e}")
            # 标签添加失败不应该中断整个流程
    
    async def _set_advanced_options(self, page: Page, task: TaskModel):
        """设置高级选项"""
        try:
            self.logger.info("开始设置高级选项")

            # 点击"更多选项"或"显示更多"按钮
            await self._expand_advanced_options(page)

            # 设置受众类型
            await self._set_audience_settings(page, task)

            # 设置语言
            await self._set_language(page, task.language)

            # 设置播放列表（如果有）
            if task.playlist:
                await self._add_to_playlist(page, task.playlist)

            # 设置其他选项
            await self._set_other_options(page, task)

            self.logger.info("高级选项设置完成")

        except Exception as e:
            raise FormFillError(f"设置高级选项失败: {e}") from e

    async def _expand_advanced_options(self, page: Page):
        """展开高级选项"""
        try:
            more_options_selectors = self.selectors.get_selector("upload", "more_options_button")

            for selector in more_options_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        # 检查是否可见
                        is_visible = await element.is_visible()
                        if is_visible:
                            await element.click()
                            await self._random_delay(1, 2)
                            return
                except Exception as e:
                    self.logger.debug(f"展开高级选项失败 ({selector}): {e}")
                    continue

            # 如果没有找到"更多选项"按钮，可能已经展开了
            self.logger.debug("未找到更多选项按钮，可能已经展开")

        except Exception as e:
            self.logger.warning(f"展开高级选项失败: {e}")

    async def _set_audience_settings(self, page: Page, task: TaskModel):
        """设置受众设置 (修复版本 - 确保必选项被选择)"""
        try:
            self.logger.info("设置受众选项")

            # 检测并清除任何错误提示
            await self._check_and_clear_error_messages(page)

            # 设置是否为儿童内容 (这是必选项!)
            made_for_kids = getattr(task, 'audience_made_for_kids', False)

            self.logger.info(f"设置儿童内容选项: {made_for_kids}")

            if made_for_kids:
                success = await self._select_radio_option_enhanced(page, "audience", "made_for_kids_yes", "儿童内容-是")
            else:
                success = await self._select_radio_option_enhanced(page, "audience", "made_for_kids_no", "儿童内容-否")

            if not success:
                raise FormFillError("无法选择儿童内容选项 (必选项)")

            await self._random_delay(0.5, 1.0)

            # 检查是否还有错误提示
            await self._check_and_clear_error_messages(page)

            # 设置年龄限制 (展开高级选项)
            await self._expand_age_restriction_section(page)

            age_restriction = getattr(task, 'age_restriction_18plus', False)
            self.logger.info(f"设置年龄限制选项: {age_restriction}")

            if age_restriction:
                await self._select_radio_option_enhanced(page, "audience", "age_restriction_yes", "年龄限制-是")
            else:
                await self._select_radio_option_enhanced(page, "audience", "age_restriction_no", "年龄限制-否")

            await self._random_delay(0.5, 1.0)

        except Exception as e:
            self.logger.warning(f"设置受众选项失败: {e}")

    async def _set_language(self, page: Page, language: str):
        """设置语言"""
        try:
            language_selectors = self.selectors.get_selector("upload", "language_dropdown")

            for selector in language_selectors:
                try:
                    # 点击语言下拉框
                    await page.click(selector)
                    await self._random_delay(0.5, 1)

                    # 查找对应的语言选项
                    language_option_selector = f"[data-value='{language}'], [value='{language}']"
                    await page.click(language_option_selector)

                    self.logger.debug(f"成功设置语言: {language}")
                    return

                except Exception as e:
                    self.logger.debug(f"设置语言失败 ({selector}): {e}")
                    continue

            self.logger.warning(f"无法设置语言: {language}")

        except Exception as e:
            self.logger.warning(f"设置语言失败: {e}")

    async def _add_to_playlist(self, page: Page, playlist_name: str):
        """添加到播放列表"""
        try:
            self.logger.info(f"添加到播放列表: {playlist_name}")

            playlist_selectors = self.selectors.get_selector("upload", "playlist_dropdown")

            for selector in playlist_selectors:
                try:
                    # 点击播放列表下拉框
                    await page.click(selector)
                    await self._random_delay(0.5, 1)

                    # 查找播放列表选项
                    playlist_option = await page.query_selector(f"text='{playlist_name}'")
                    if playlist_option:
                        await playlist_option.click()
                        self.logger.debug(f"成功添加到播放列表: {playlist_name}")
                        return

                except Exception as e:
                    self.logger.debug(f"添加到播放列表失败 ({selector}): {e}")
                    continue

            self.logger.warning(f"无法找到播放列表: {playlist_name}")

        except Exception as e:
            self.logger.warning(f"添加到播放列表失败: {e}")

    async def _set_other_options(self, page: Page, task: TaskModel):
        """设置其他选项 (修复版本 - 确保所有选项都被处理)"""
        try:
            self.logger.info("设置其他选项")

            # 检测并清除任何错误提示
            await self._check_and_clear_error_messages(page)

            # 设置Shorts混剪选项
            remix_option = getattr(task, 'remix_option', 'video_audio')
            self.logger.info(f"设置Shorts混剪选项: {remix_option}")

            if remix_option == 'audio_only':
                await self._select_radio_option_enhanced(page, "audience", "remix_allow_audio_only", "混剪-仅音频")
            else:
                # 默认允许视频和音频混剪
                await self._select_radio_option_enhanced(page, "audience", "remix_allow_video_audio", "混剪-视频音频")

            await self._random_delay(0.5, 1.0)

            # 设置Altered content选项
            altered_content = getattr(task, 'altered_content', False)
            self.logger.info(f"设置Altered content选项: {altered_content}")

            if altered_content:
                await self._select_radio_option_enhanced(page, "audience", "altered_content_yes", "Altered content-是")
            else:
                # 默认设置为No
                await self._select_radio_option_enhanced(page, "audience", "altered_content_no", "Altered content-否")

            await self._random_delay(0.5, 1.0)

            # 检查是否还有错误提示
            await self._check_and_clear_error_messages(page)

        except Exception as e:
            self.logger.warning(f"设置其他选项失败: {e}")

    async def _select_radio_option(self, page: Page, group: str, option: str):
        """选择单选按钮选项 (旧版本)"""
        try:
            selectors = self.selectors.get_selector("upload", f"{group}_{option}")

            for selector in selectors:
                try:
                    await page.click(selector)
                    self.logger.debug(f"成功选择选项: {group}_{option}")
                    return
                except Exception as e:
                    self.logger.debug(f"选择选项失败 ({selector}): {e}")
                    continue

            self.logger.warning(f"无法找到选项: {group}_{option}")

        except Exception as e:
            self.logger.warning(f"选择单选选项失败: {e}")

    async def _select_radio_option_new(self, page: Page, group: str, option: str):
        """选择单选按钮选项 (基于MCP分析结果的新版本)"""
        try:
            # 使用新的AUDIENCE_SELECTORS
            selectors = self.selectors.get_selector("audience", option)

            if not selectors:
                self.logger.warning(f"未找到选择器配置: audience.{option}")
                return

            for selector in selectors:
                try:
                    # 首先检查元素是否存在且可见
                    element = await page.query_selector(selector)
                    if element:
                        is_visible = await element.is_visible()
                        if is_visible:
                            await element.click()
                            await self._random_delay(0.3, 0.7)
                            self.logger.debug(f"成功选择选项: {option} 使用选择器: {selector}")
                            return
                        else:
                            self.logger.debug(f"元素不可见: {selector}")
                    else:
                        self.logger.debug(f"元素不存在: {selector}")
                except Exception as e:
                    self.logger.debug(f"选择选项失败 ({selector}): {e}")
                    continue

            self.logger.warning(f"无法找到或点击选项: {option}")

        except Exception as e:
            self.logger.warning(f"选择单选选项失败: {e}")

    async def _select_checkbox(self, page: Page, group: str, option: str, checked: bool):
        """选择复选框选项"""
        try:
            selectors = self.selectors.get_selector("upload", f"{group}_{option}")

            for selector in selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        is_checked = await element.is_checked()
                        if is_checked != checked:
                            await element.click()
                        self.logger.debug(f"成功设置复选框: {group}_{option} = {checked}")
                        return
                except Exception as e:
                    self.logger.debug(f"设置复选框失败 ({selector}): {e}")
                    continue

            self.logger.warning(f"无法找到复选框: {group}_{option}")

        except Exception as e:
            self.logger.warning(f"设置复选框失败: {e}")

    async def _check_and_clear_error_messages(self, page: Page):
        """检测并清除错误提示浮层"""
        try:
            # 检测常见的错误提示
            error_messages = [
                "You need to answer this question",
                "This field is required",
                "Please select an option",
                "Required field",
            ]

            for message in error_messages:
                try:
                    error_element = await page.query_selector(f'text="{message}"')
                    if error_element and await error_element.is_visible():
                        self.logger.warning(f"检测到错误提示: {message}")

                        # 尝试点击关闭按钮或者按ESC键
                        close_selectors = [
                            'button[aria-label="Close"]',
                            'button[aria-label="Dismiss"]',
                            '.close-button',
                            '[role="button"]:has-text("×")',
                        ]

                        for close_selector in close_selectors:
                            try:
                                close_button = await page.query_selector(close_selector)
                                if close_button and await close_button.is_visible():
                                    await close_button.click()
                                    await asyncio.sleep(0.5)
                                    break
                            except:
                                continue

                        # 如果没有找到关闭按钮，按ESC键
                        await page.keyboard.press('Escape')
                        await asyncio.sleep(0.5)

                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"检测错误提示失败: {e}")
            return False

    async def _fill_element_with_selectors_enhanced(self, page: Page, selectors: list, value: str, element_name: str, required: bool = False):
        """增强的元素填写方法 (修复contenteditable支持)"""
        try:
            self.logger.debug(f"尝试填写{element_name}: {value}")

            for selector in selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        is_visible = await element.is_visible()
                        if is_visible:
                            # 滚动到元素位置
                            await element.scroll_into_view_if_needed()
                            await asyncio.sleep(0.3)

                            # 点击元素获得焦点
                            await element.click()
                            await asyncio.sleep(0.3)

                            # 检查元素类型
                            tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                            is_contenteditable = await element.evaluate('el => el.contentEditable === "true"')

                            if is_contenteditable or tag_name == 'div':
                                # 处理contenteditable元素 (YouTube的富文本编辑器)
                                self.logger.debug(f"检测到contenteditable元素: {selector}")

                                # 清空现有内容
                                await page.keyboard.press('Control+a')
                                await asyncio.sleep(0.1)
                                await page.keyboard.press('Delete')
                                await asyncio.sleep(0.3)

                                # 输入新内容
                                await page.keyboard.type(value)
                                await asyncio.sleep(0.5)

                                # 验证填写结果 (对于contenteditable使用textContent)
                                try:
                                    filled_value = await element.evaluate('el => el.textContent || el.innerText || ""')
                                    self.logger.debug(f"contenteditable验证 - 期望: {value}, 实际: {filled_value}")
                                    if filled_value and value in filled_value:
                                        self.logger.info(f"✅ 成功填写{element_name} (contenteditable): {selector}")
                                        return True
                                    else:
                                        # 即使验证失败，也可能填写成功了，给一个机会
                                        self.logger.info(f"✅ 填写{element_name}完成 (contenteditable, 跳过验证): {selector}")
                                        return True
                                except Exception as verify_error:
                                    self.logger.debug(f"contenteditable验证失败: {verify_error}")
                                    # 验证失败但填写可能成功
                                    self.logger.info(f"✅ 填写{element_name}完成 (contenteditable, 验证异常): {selector}")
                                    return True

                            else:
                                # 处理普通input/textarea元素
                                self.logger.debug(f"检测到普通输入元素: {selector}")

                                # 清空现有内容
                                await page.keyboard.press('Control+a')
                                await asyncio.sleep(0.1)

                                # 尝试fill方法
                                try:
                                    await element.fill(value)
                                    await asyncio.sleep(0.3)

                                    # 验证填写结果
                                    filled_value = await element.input_value()
                                    if filled_value and filled_value.strip():
                                        self.logger.info(f"成功填写{element_name} (fill方法): {selector}")
                                        return True
                                except:
                                    # fill失败，尝试type方法
                                    await page.keyboard.press('Control+a')
                                    await page.keyboard.type(value)
                                    await asyncio.sleep(0.3)

                                    try:
                                        filled_value = await element.input_value()
                                        if filled_value and filled_value.strip():
                                            self.logger.info(f"成功填写{element_name} (type方法): {selector}")
                                            return True
                                    except:
                                        # 如果input_value也失败，尝试textContent
                                        filled_value = await element.evaluate('el => el.value || el.textContent || el.innerText')
                                        if filled_value and filled_value.strip():
                                            self.logger.info(f"成功填写{element_name} (textContent验证): {selector}")
                                            return True
                        else:
                            self.logger.debug(f"元素不可见: {selector}")
                    else:
                        self.logger.debug(f"元素不存在: {selector}")
                except Exception as e:
                    self.logger.debug(f"填写{element_name}失败 ({selector}): {e}")
                    continue

            if required:
                self.logger.error(f"无法填写必需的{element_name}: {value}")
            else:
                self.logger.warning(f"无法填写{element_name}: {value}")

            return False

        except Exception as e:
            self.logger.warning(f"填写{element_name}失败: {e}")
            return False

    async def _select_playlist(self, page: Page, playlist_name: str):
        """选择播放列表"""
        try:
            self.logger.info(f"选择播放列表: {playlist_name}")

            # 点击播放列表下拉框
            playlist_selectors = [
                'text="Select"',
                '[aria-label*="playlist"]',
                'div:has-text("Select")',
                '.playlist-selector',
            ]

            for selector in playlist_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        await element.click()
                        await asyncio.sleep(1)
                        break
                except:
                    continue

            # 查找并点击指定的播放列表
            playlist_option_selectors = [
                f'text="{playlist_name}"',
                f'[aria-label*="{playlist_name}"]',
                f'div:has-text("{playlist_name}")',
            ]

            for selector in playlist_option_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        await element.click()
                        await asyncio.sleep(0.5)
                        self.logger.info(f"成功选择播放列表: {playlist_name}")
                        return True
                except:
                    continue

            self.logger.warning(f"无法找到播放列表: {playlist_name}")
            return False

        except Exception as e:
            self.logger.warning(f"选择播放列表失败: {e}")
            return False

    async def _validate_required_fields(self, page: Page):
        """验证所有必选项是否已填写"""
        try:
            self.logger.info("验证必选项")

            # 检查标题是否已填写
            title_selectors = self.selectors.get_selector("upload", "title_input")
            title_filled = False

            for selector in title_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        value = await element.input_value()
                        if value and value.strip():
                            title_filled = True
                            break
                except:
                    continue

            if not title_filled:
                self.logger.warning("标题未填写，这是必选项")

            # 检查儿童内容选项是否已选择
            kids_options_checked = False
            kids_selectors = [
                'input[name*="kids"]:checked',
                'tp-yt-paper-radio-button[aria-checked="true"]',
                '[role="radio"][aria-checked="true"]',
            ]

            for selector in kids_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        kids_options_checked = True
                        break
                except:
                    continue

            if not kids_options_checked:
                self.logger.warning("儿童内容选项未选择，这是必选项")

            # 检查是否有错误提示
            error_found = await self._check_and_clear_error_messages(page)

            if error_found or not title_filled or not kids_options_checked:
                self.logger.warning("发现未完成的必选项")
                return False

            self.logger.info("所有必选项验证通过")
            return True

        except Exception as e:
            self.logger.warning(f"验证必选项失败: {e}")
            return False

    async def _select_radio_option_enhanced(self, page: Page, group: str, option: str, description: str):
        """增强的单选按钮选择方法"""
        try:
            self.logger.info(f"选择选项: {description}")

            # 使用新的AUDIENCE_SELECTORS
            selectors = self.selectors.get_selector("audience", option)

            if not selectors:
                self.logger.warning(f"未找到选择器配置: audience.{option}")
                return False

            for selector in selectors:
                try:
                    # 首先检查元素是否存在且可见
                    element = await page.query_selector(selector)
                    if element:
                        is_visible = await element.is_visible()
                        if is_visible:
                            # 滚动到元素位置
                            await element.scroll_into_view_if_needed()
                            await asyncio.sleep(0.3)

                            # 点击元素
                            await element.click()
                            await self._random_delay(0.3, 0.7)

                            # 验证选择是否成功
                            await asyncio.sleep(0.5)

                            self.logger.info(f"成功选择选项: {description} 使用选择器: {selector}")
                            return True
                        else:
                            self.logger.debug(f"元素不可见: {selector}")
                    else:
                        self.logger.debug(f"元素不存在: {selector}")
                except Exception as e:
                    self.logger.debug(f"选择选项失败 ({selector}): {e}")
                    continue

            self.logger.error(f"无法找到或点击选项: {description}")
            return False

        except Exception as e:
            self.logger.error(f"选择选项失败: {description} - {e}")
            return False

    async def _expand_age_restriction_section(self, page: Page):
        """展开年龄限制高级选项"""
        try:
            self.logger.info("展开年龄限制高级选项")

            # 查找年龄限制展开按钮
            expand_selectors = [
                'text="Age restriction (advanced)"',
                '[aria-label*="Age restriction"]',
                'button:has-text("Age restriction")',
                'div:has-text("Age restriction (advanced)")',
            ]

            for selector in expand_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        # 检查是否已经展开
                        parent = await element.query_selector('..')
                        if parent:
                            # 查找展开状态指示器
                            expanded = await parent.query_selector('[aria-expanded="true"]')
                            if not expanded:
                                await element.click()
                                await asyncio.sleep(1)
                                self.logger.info("成功展开年龄限制选项")
                            else:
                                self.logger.info("年龄限制选项已经展开")
                        return True
                except Exception as e:
                    self.logger.debug(f"展开年龄限制失败 ({selector}): {e}")
                    continue

            self.logger.warning("未找到年龄限制展开按钮")
            return False

        except Exception as e:
            self.logger.warning(f"展开年龄限制选项失败: {e}")
            return False

    async def _handle_ypp_section(self, page: Page):
        """处理 YPP 广告适合度部分"""
        try:
            self.logger.info("检查 YPP 广告适合度部分")

            # 检查是否存在YPP部分
            ypp_selectors = self.selectors.get_selector("upload", "ypp_section")
            ypp_found = False

            for selector in ypp_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        ypp_found = True
                        break
                except:
                    continue

            if ypp_found:
                self.logger.info("发现YPP部分，进行自动处理")

                # 选择"以上都没有"选项
                none_selectors = self.selectors.get_selector("upload", "ypp_none_option")
                for selector in none_selectors:
                    try:
                        await page.click(selector)
                        self.logger.debug(f"成功选择'以上都没有': {selector}")
                        break
                    except Exception as e:
                        self.logger.debug(f"选择YPP选项失败 ({selector}): {e}")
                        continue

                # 点击继续或下一步按钮
                continue_selectors = self.selectors.get_selector("upload", "ypp_continue_button")
                for selector in continue_selectors:
                    try:
                        await page.click(selector)
                        await self._random_delay(1, 2)
                        self.logger.debug(f"成功点击YPP继续按钮: {selector}")
                        break
                    except Exception as e:
                        self.logger.debug(f"点击YPP继续按钮失败 ({selector}): {e}")
                        continue
            else:
                self.logger.info("未发现YPP部分，跳过")

            self.logger.info("YPP 部分处理完成")

        except Exception as e:
            self.logger.warning(f"处理YPP部分失败: {e}")
            # YPP部分处理失败不应该中断整个流程
    
    async def _set_visibility(self, page: Page, task: TaskModel):
        """设置可见性 (基于MCP分析结果更新)"""
        try:
            self.logger.info(f"设置可见性: {task.visibility}")

            # 首先导航到可见性页面 (点击Next按钮)
            await self._navigate_to_visibility_page(page)

            await self._random_delay(1, 2)

            # 根据可见性类型选择相应选项
            if task.visibility == "public":
                await self._select_visibility_option(page, "public")
            elif task.visibility == "unlisted":
                await self._select_visibility_option(page, "unlisted")
            elif task.visibility == "private":
                await self._select_visibility_option(page, "private")
            elif task.visibility == "scheduled":
                await self._set_scheduled_visibility(page, task)
            else:
                self.logger.warning(f"未知的可见性类型: {task.visibility}")
                # 默认设置为未列出
                await self._select_visibility_option(page, "unlisted")

            self.logger.info("可见性设置完成")

        except Exception as e:
            raise FormFillError(f"设置可见性失败: {e}") from e

    async def _navigate_to_visibility_page(self, page: Page):
        """导航到可见性页面 (修复版本 - 验证必选项后点击Next)"""
        try:
            self.logger.info("导航到可见性页面")

            # 首先验证所有必选项是否已选择
            await self._validate_required_fields(page)

            # 可能需要点击多次Next按钮
            max_next_clicks = 3

            for click_count in range(max_next_clicks):
                # 检查是否已经在可见性页面
                visibility_indicators = ["Public", "Unlisted", "Private", "Scheduled"]
                visibility_found = False

                for indicator in visibility_indicators:
                    try:
                        element = await page.query_selector(f'text="{indicator}"')
                        if element and await element.is_visible():
                            self.logger.info(f"已到达可见性页面，找到: {indicator}")
                            visibility_found = True
                            break
                    except:
                        continue

                if visibility_found:
                    break

                # 在点击Next之前检查错误提示
                has_errors = await self._check_and_clear_error_messages(page)
                if has_errors:
                    self.logger.warning("检测到错误提示，重新验证必选项")
                    await self._validate_required_fields(page)

                # 点击Next按钮
                next_selectors = self.selectors.get_selector("navigation", "next_button")
                next_clicked = False

                for selector in next_selectors:
                    try:
                        element = await page.query_selector(selector)
                        if element and await element.is_visible():
                            # 检查按钮是否可点击
                            is_enabled = await element.is_enabled()
                            if is_enabled:
                                await element.click()
                                await self._random_delay(2, 3)
                                self.logger.debug(f"点击Next按钮 (第{click_count + 1}次): {selector}")
                                next_clicked = True
                                break
                            else:
                                self.logger.warning(f"Next按钮不可点击，可能有必选项未填写")
                    except Exception as e:
                        self.logger.debug(f"点击Next失败 ({selector}): {e}")
                        continue

                if not next_clicked:
                    self.logger.warning(f"无法点击Next按钮 (第{click_count + 1}次)")
                    # 再次检查错误提示
                    await self._check_and_clear_error_messages(page)
                    break

            # 最终检查是否到达可见性页面
            visibility_found = False
            for indicator in visibility_indicators:
                try:
                    element = await page.query_selector(f'text="{indicator}"')
                    if element and await element.is_visible():
                        visibility_found = True
                        break
                except:
                    continue

            if not visibility_found:
                self.logger.error("未成功到达可见性页面，可能有必选项未填写")
                await self._check_and_clear_error_messages(page)
                raise FormFillError("无法导航到可见性页面，请检查必选项是否已填写")

        except Exception as e:
            self.logger.error(f"导航到可见性页面失败: {e}")
            raise

    async def _select_visibility_option(self, page: Page, visibility_type: str):
        """选择可见性选项 (基于MCP分析结果更新)"""
        try:
            # 使用新的VISIBILITY_SELECTORS
            visibility_selectors = self.selectors.get_selector("visibility", f"{visibility_type}_option")

            for selector in visibility_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        await element.click()
                        await self._random_delay(0.5, 1.0)
                        self.logger.debug(f"成功选择可见性: {visibility_type} 使用选择器: {selector}")
                        return
                except Exception as e:
                    self.logger.debug(f"选择可见性失败 ({selector}): {e}")
                    continue

            raise FormFillError(f"无法找到可见性选项: {visibility_type}")

        except Exception as e:
            raise FormFillError(f"选择可见性选项失败: {e}") from e

    async def _set_scheduled_visibility(self, page: Page, task: TaskModel):
        """设置定时发布"""
        try:
            # 选择定时发布选项
            await self._select_visibility_option(page, "scheduled")

            # 设置发布时间
            if task.schedule_time:
                schedule_dt = task.get_schedule_datetime()
                if schedule_dt:
                    await self._set_schedule_time(page, schedule_dt)
                else:
                    self.logger.warning("无效的定时发布时间格式")
            else:
                self.logger.warning("定时发布但未提供时间")

        except Exception as e:
            raise FormFillError(f"设置定时发布失败: {e}") from e

    async def _set_schedule_time(self, page: Page, schedule_dt: datetime):
        """设置具体的发布时间"""
        try:
            # 设置日期
            date_selectors = self.selectors.get_selector("upload", "schedule_date_input")
            date_str = schedule_dt.strftime("%Y-%m-%d")

            for selector in date_selectors:
                try:
                    await page.fill(selector, date_str)
                    break
                except Exception as e:
                    self.logger.debug(f"设置日期失败 ({selector}): {e}")
                    continue

            # 设置时间
            time_selectors = self.selectors.get_selector("upload", "schedule_time_input")
            time_str = schedule_dt.strftime("%H:%M")

            for selector in time_selectors:
                try:
                    await page.fill(selector, time_str)
                    break
                except Exception as e:
                    self.logger.debug(f"设置时间失败 ({selector}): {e}")
                    continue

            self.logger.info(f"设置发布时间: {schedule_dt}")

        except Exception as e:
            raise FormFillError(f"设置发布时间失败: {e}") from e

    async def _publish_video(self, page: Page, result: ResultModel) -> tuple:
        """发布视频"""
        try:
            self.logger.info("开始发布视频")

            # 点击发布按钮
            publish_selectors = self.selectors.get_selector("upload", "publish_button")

            for selector in publish_selectors:
                try:
                    await page.click(selector)
                    self.logger.debug(f"成功点击发布按钮: {selector}")
                    break
                except Exception as e:
                    self.logger.debug(f"点击发布按钮失败 ({selector}): {e}")
                    continue
            else:
                raise PublishError("无法找到发布按钮")

            # 等待发布完成
            await self._wait_for_publish_complete(page)

            # 获取视频链接和ID
            video_url, video_id = await self._extract_video_info(page)

            # 更新结果
            result.video_url = video_url
            result.video_id = video_id

            self.logger.info(f"视频发布完成: {video_url}")
            return video_url, video_id

        except Exception as e:
            raise PublishError(f"发布视频失败: {e}") from e

    async def _wait_for_publish_complete(self, page: Page):
        """等待发布完成"""
        try:
            # 等待发布完成的标志
            success_selectors = self.selectors.get_selector("upload", "publish_success")

            for selector in success_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=60000)  # 等待1分钟
                    self.logger.debug(f"发布完成标志出现: {selector}")
                    return
                except Exception as e:
                    self.logger.debug(f"等待发布完成失败 ({selector}): {e}")
                    continue

            # 如果没有找到明确的成功标志，等待一段时间
            await asyncio.sleep(5)
            self.logger.warning("未找到明确的发布完成标志，继续执行")

        except Exception as e:
            self.logger.warning(f"等待发布完成失败: {e}")

    async def _extract_video_info(self, page: Page) -> Tuple[Optional[str], Optional[str]]:
        """提取视频链接和ID"""
        try:
            video_url = None
            video_id = None

            # 尝试从页面URL获取视频ID
            current_url = page.url
            if "youtube.com" in current_url:
                import re
                # 匹配YouTube视频ID模式
                match = re.search(r'[?&]v=([a-zA-Z0-9_-]{11})', current_url)
                if match:
                    video_id = match.group(1)
                    video_url = f"https://www.youtube.com/watch?v={video_id}"

            # 尝试从页面元素获取链接
            if not video_url:
                link_selectors = self.selectors.get_selector("upload", "video_link")
                for selector in link_selectors:
                    try:
                        element = await page.query_selector(selector)
                        if element:
                            href = await element.get_attribute("href")
                            if href and "youtube.com/watch" in href:
                                video_url = href
                                # 从URL提取ID
                                match = re.search(r'[?&]v=([a-zA-Z0-9_-]{11})', href)
                                if match:
                                    video_id = match.group(1)
                                break
                    except Exception as e:
                        self.logger.debug(f"提取视频链接失败 ({selector}): {e}")
                        continue

            if video_url:
                self.logger.info(f"成功提取视频信息: {video_url}")
            else:
                self.logger.warning("无法提取视频链接")

            return video_url, video_id

        except Exception as e:
            self.logger.warning(f"提取视频信息失败: {e}")
            return None, None
    
    async def _save_error_screenshot(self, page: Page, task_id: str) -> Optional[str]:
        """保存错误截图"""
        try:
            screenshot_dir = Path(self.config.get('screenshot_dir', 'screenshots'))
            screenshot_dir.mkdir(parents=True, exist_ok=True)

            timestamp = int(time.time())
            screenshot_path = screenshot_dir / f"error_{task_id}_{timestamp}.png"

            await page.screenshot(path=str(screenshot_path), full_page=True)

            self.logger.info(f"错误截图已保存: {screenshot_path}")
            return str(screenshot_path)

        except Exception as e:
            self.logger.error(f"保存错误截图失败: {e}")
            return None

    async def _collect_diagnostic_info(self, page: Page, task: TaskModel, error: Exception) -> Dict[str, Any]:
        """收集诊断信息"""
        try:
            diagnostic_info = {
                "error_type": type(error).__name__,
                "error_message": str(error),
                "timestamp": datetime.now().isoformat(),
                "task_info": {
                    "profile_name": task.profile_name,
                    "video_path": task.video_path,
                    "title": task.title,
                    "visibility": task.visibility
                },
                "page_info": {},
                "browser_info": {},
                "upload_progress": {}
            }

            # 收集页面信息
            try:
                diagnostic_info["page_info"] = {
                    "url": page.url,
                    "title": await page.title(),
                    "viewport": await page.viewport_size()
                }
            except Exception as e:
                self.logger.debug(f"收集页面信息失败: {e}")

            # 收集浏览器信息
            try:
                user_agent = await page.evaluate("navigator.userAgent")
                diagnostic_info["browser_info"] = {
                    "user_agent": user_agent,
                    "language": await page.evaluate("navigator.language"),
                    "platform": await page.evaluate("navigator.platform")
                }
            except Exception as e:
                self.logger.debug(f"收集浏览器信息失败: {e}")

            # 收集上传进度信息
            if self._current_progress:
                diagnostic_info["upload_progress"] = {
                    "stage": self._current_progress.stage,
                    "progress_percent": self._current_progress.progress_percent,
                    "current_step": self._current_progress.current_step,
                    "completed_steps": self._current_progress.completed_steps,
                    "error_count": self._current_progress.error_count,
                    "retry_count": self._current_progress.retry_count,
                    "elapsed_time": str(self._current_progress.get_elapsed_time())
                }

            # 检查页面上的错误信息
            try:
                error_elements = await page.query_selector_all("[role='alert'], .error, .warning")
                error_messages = []
                for element in error_elements:
                    try:
                        text = await element.text_content()
                        if text and text.strip():
                            error_messages.append(text.strip())
                    except:
                        continue

                if error_messages:
                    diagnostic_info["page_errors"] = error_messages

            except Exception as e:
                self.logger.debug(f"收集页面错误信息失败: {e}")

            return diagnostic_info

        except Exception as e:
            self.logger.error(f"收集诊断信息失败: {e}")
            return {
                "error_type": type(error).__name__,
                "error_message": str(error),
                "diagnostic_collection_error": str(e)
            }

    def get_upload_statistics(self) -> Dict[str, Any]:
        """获取上传统计信息"""
        if not self._current_progress:
            return {}

        return {
            "current_stage": self._current_progress.stage,
            "progress_percent": self._current_progress.progress_percent,
            "elapsed_time": str(self._current_progress.get_elapsed_time()),
            "estimated_remaining": str(self._current_progress.estimate_remaining_time()) if self._current_progress.estimate_remaining_time() else None,
            "error_count": self._current_progress.error_count,
            "retry_count": self._current_progress.retry_count,
            "completed_steps": self._current_progress.completed_steps,
            "total_steps": self._current_progress.total_steps
        }

    def reset_progress(self):
        """重置进度信息"""
        self._current_progress = None
        self._upload_start_time = None
