#!/usr/bin/env python3
"""
测试优化后的YouTube上传功能
基于真实录制的选择器
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('optimized_upload_test.log', encoding='utf-8')
        ]
    )

async def test_optimized_upload():
    """测试优化后的上传功能"""
    print("🚀 测试优化后的YouTube上传功能")
    print("=" * 60)
    
    try:
        # 1. 读取任务
        print("\n1️⃣ 读取任务数据...")
        from youtube_uploader.excel_handler import ExcelHandler
        
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        
        if not tasks:
            print("❌ 未找到任务数据")
            return False
        
        task = tasks[0]
        print(f"✅ 读取任务: {task.title} ({task.profile_name})")
        
        # 2. 初始化执行器
        print(f"\n2️⃣ 初始化执行器...")
        from youtube_uploader.executor import TaskExecutor
        
        config = {
            'max_retries': 3,
            'retry_delay': 5,
            'page_load_timeout': 30,
            'element_timeout': 15,
            'upload_timeout': 600,
            'screenshot_dir': 'screenshots'
        }
        
        executor = TaskExecutor(config)
        print("✅ 执行器初始化成功")
        
        # 3. 检查profile
        print(f"\n3️⃣ 检查profile...")
        if not executor.bit_browser_manager.check_profile_exists(task.profile_name):
            print(f"❌ Profile '{task.profile_name}' 不存在")
            return False
        
        print(f"✅ Profile '{task.profile_name}' 存在")
        
        # 4. 打开浏览器
        print(f"\n4️⃣ 打开浏览器...")
        ws_address = executor.bit_browser_manager.open_browser(task.profile_name)
        
        if not ws_address:
            print("❌ 无法打开浏览器")
            return False
        
        print(f"✅ 浏览器已连接: {ws_address}")
        
        # 5. 执行上传
        print(f"\n5️⃣ 开始上传视频...")
        print(f"📹 视频: {task.video_path}")
        print(f"📝 标题: {task.title}")
        print(f"👁️ 可见性: {task.visibility.value}")
        
        # 连接到浏览器并执行上传
        from playwright.async_api import async_playwright
        
        async with async_playwright() as p:
            browser = await p.chromium.connect_over_cdp(ws_address)
            context = browser.contexts[0] if browser.contexts else await browser.new_context()
            page = await context.new_page()
            
            try:
                # 使用优化的自动化模块
                result = await executor.youtube_automation.upload_video(page, task)
                
                # 显示结果
                print(f"\n📊 上传结果:")
                print(f"状态: {result.status.value}")
                
                if result.status.value == "success":
                    print(f"✅ 上传成功!")
                    if result.video_url:
                        print(f"🔗 视频链接: {result.video_url}")
                    if result.video_id:
                        print(f"🆔 视频ID: {result.video_id}")
                else:
                    print(f"❌ 上传失败")
                    if result.error_message:
                        print(f"错误信息: {result.error_message}")
                    if result.screenshot_path:
                        print(f"错误截图: {result.screenshot_path}")
                
                return result.status.value == "success"
                
            finally:
                await page.close()
                await browser.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    setup_logging()
    
    print("🎯 优化后的YouTube上传测试")
    
    # 检查必要文件
    if not Path('tasks_template.xlsx').exists():
        print("❌ 未找到 tasks_template.xlsx 文件")
        return
    
    # 运行测试
    result = asyncio.run(test_optimized_upload())
    
    print(f"\n{'='*60}")
    if result:
        print("🎉 测试成功！优化后的上传功能正常工作！")
        print("✅ 现在可以使用 python run_upload.py 进行批量上传")
    else:
        print("❌ 测试失败！需要进一步调试")
    print("="*60)

if __name__ == "__main__":
    main()
