#!/usr/bin/env python3
"""
最终验证脚本
确认profile检测问题已完全修复
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )

async def verify_profile_detection():
    """验证profile检测功能"""
    print("🔍 最终验证：Profile检测功能")
    print("=" * 60)
    
    try:
        # 1. 验证Excel读取
        print("\n1️⃣ 验证Excel读取...")
        from youtube_uploader.excel_handler import ExcelHandler
        
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        
        if not tasks:
            print("❌ 未找到任务数据")
            return False
        
        print(f"✅ 成功读取 {len(tasks)} 个任务")
        for i, task in enumerate(tasks):
            print(f"   任务 {i+1}: {task.title} ({task.profile_name})")
        
        # 2. 验证TaskExecutor初始化
        print(f"\n2️⃣ 验证TaskExecutor初始化...")
        from youtube_uploader.executor import TaskExecutor
        
        config = {
            'max_retries': 3,
            'retry_delay': 5,
            'page_load_timeout': 30,
            'element_timeout': 15,
            'upload_timeout': 600,
            'screenshot_dir': 'screenshots'
        }
        
        executor = TaskExecutor(config)
        print("✅ TaskExecutor初始化成功")
        
        # 3. 验证BitBrowserManager
        print(f"\n3️⃣ 验证BitBrowserManager...")
        bit_manager = executor.bit_browser_manager
        
        print(f"✅ BitBrowserManager配置:")
        print(f"   - Website: {bit_manager.website}")
        print(f"   - Max retries: {bit_manager.max_retries}")
        print(f"   - Retry delay: {bit_manager.retry_delay}")
        
        # 4. 验证比特浏览器连接
        print(f"\n4️⃣ 验证比特浏览器连接...")
        if not bit_manager.is_bit_browser_running():
            print("❌ 比特浏览器未运行")
            return False
        else:
            print("✅ 比特浏览器运行正常")
        
        # 5. 验证profile检测
        print(f"\n5️⃣ 验证profile检测...")
        all_success = True
        
        for task in tasks:
            profile_name = task.profile_name
            print(f"\n检测profile: '{profile_name}'")
            
            try:
                # 检查profile是否存在
                exists = bit_manager.check_profile_exists(profile_name)
                if exists:
                    print(f"✅ Profile '{profile_name}' 存在")
                    
                    # 获取浏览器ID
                    browser_id = bit_manager.get_browser_id(profile_name)
                    print(f"✅ 浏览器ID: {browser_id}")
                    
                    # 获取浏览器信息
                    browser_info = bit_manager.get_browser_info(profile_name)
                    if browser_info:
                        print(f"✅ 浏览器信息获取成功")
                        print(f"   - 名称: {browser_info.get('name', 'N/A')}")
                        print(f"   - 平台: {browser_info.get('platform', 'N/A')}")
                        print(f"   - 状态: {browser_info.get('status', 'N/A')}")
                    else:
                        print(f"⚠️ 无法获取浏览器信息")
                        
                else:
                    print(f"❌ Profile '{profile_name}' 不存在")
                    all_success = False
                    
            except Exception as e:
                print(f"❌ 检测profile时出错: {e}")
                all_success = False
        
        # 6. 验证任务执行流程（模拟）
        print(f"\n6️⃣ 验证任务执行流程（模拟）...")
        
        for task in tasks:
            profile_name = task.profile_name
            print(f"\n模拟执行profile '{profile_name}' 的任务...")
            
            # 检查 profile 是否存在
            if not bit_manager.check_profile_exists(profile_name):
                print(f"❌ Profile '{profile_name}' 不存在，跳过相关任务")
                all_success = False
                continue
            else:
                print(f"✅ Profile '{profile_name}' 检测通过")
            
            # 模拟获取浏览器ID
            try:
                browser_id = bit_manager.get_browser_id(profile_name)
                print(f"✅ 浏览器ID获取成功: {browser_id}")
            except Exception as e:
                print(f"❌ 浏览器ID获取失败: {e}")
                all_success = False
                continue
            
            print(f"✅ 模拟执行成功")
        
        print(f"\n" + "=" * 60)
        if all_success:
            print("🎉 所有验证通过！Profile检测功能完全正常！")
            print("✅ 程序现在可以正确识别和处理profiles")
        else:
            print("❌ 部分验证失败")
        print("=" * 60)
        
        return all_success
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    setup_logging()
    
    print("🚀 Profile检测功能最终验证")
    
    # 检查必要文件
    if not Path('tasks_template.xlsx').exists():
        print("❌ 未找到 tasks_template.xlsx 文件")
        return
    
    # 运行验证
    result = asyncio.run(verify_profile_detection())
    
    print(f"\n{'='*60}")
    if result:
        print("🎉 验证成功！Profile检测问题已完全修复！")
        print("✅ 您现在可以正常运行 python run_upload.py")
    else:
        print("❌ 验证失败！仍有问题需要解决")
    print("="*60)

if __name__ == "__main__":
    main()
