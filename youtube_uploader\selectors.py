"""
界面选择器映射

存储 YouTube 界面元素的选择器，便于维护和更新。
"""

from typing import Dict, List


class YouTubeSelectors:
    """YouTube 界面选择器"""
    
    # 上传页面选择器 (基于MCP分析结果更新)
    UPLOAD_SELECTORS = {
        # 创建按钮 (已验证有效)
        "create_button": [
            'button[aria-label*="Create"]',  # 最可靠的选择器
            'button[aria-label="Create"]',
            '#create-icon',
            'button:has-text("CREATE")',
            'button:has-text("Create")',
        ],

        # 上传视频选项 (已验证有效)
        "upload_video_option": [
            'tp-yt-paper-item:has-text("Upload video")',  # 最可靠的选择器
            '[aria-label*="Upload video"]',
            'text="Upload video"',
            'text="Upload videos"',
            '[data-testid="upload-video-option"]',
        ],

        # 文件上传输入框 (需要进一步验证)
        "file_input": [
            'input[type="file"]',
            'input[accept*="video"]',
            '#file-input',
            '[data-testid="file-input"]',
            'ytcp-uploads-file-picker input',
            'input[name="Filedata"]',
        ],

        # 拖拽上传区域
        "drop_zone": [
            'ytcp-uploads-file-picker',
            '.drop-zone',
            '[data-testid="drop-zone"]',
            '.file-drop-area',
            '[aria-label*="drag"]',
            '[aria-label*="drop"]',
            '.upload-area',
        ],
        
        # 上传进度相关
        "upload_progress": [
            '[aria-label*="upload progress"]',
            '.upload-progress',
            'tp-yt-paper-progress',
            '[role="progressbar"]',
            '.progress-bar',
        ],

        # 处理完成标志
        "details_section": [
            '[aria-label="Details"]',
            'div:has-text("Details")',
            '#details-section',
            '.details-container',
        ],

        # 标题输入框 (基于MCP分析结果 - 已验证有效)
        "title_input": [
            '[aria-label="Add a title that describes your video (type @ to mention a channel)"]',  # 最准确的选择器
            '[aria-label*="Add a title"]',  # 部分匹配
            '#textbox[aria-label*="title"]',  # ID + aria-label组合
            'div[contenteditable="true"][aria-label*="title"]',  # 可编辑div + aria-label
            '[role="textbox"][aria-label*="title"]',  # role + aria-label
            '.style-scope.ytcp-social-suggestions-textbox[aria-label*="title"]',  # 类名 + aria-label
            '[aria-label="Title"]',  # 备用选择器
            '#textbox',  # 最后的备用选择器
        ],

        # 描述输入框 (基于MCP分析结果 - 已验证有效)
        "description_input": [
            '[aria-label="Tell viewers about your video (type @ to mention a channel)"]',  # 最准确的选择器
            '[aria-label*="Tell viewers about"]',  # 部分匹配
            'div[contenteditable="true"][aria-label*="viewers"]',  # 可编辑div + aria-label
            '[role="textbox"][aria-label*="viewers"]',  # role + aria-label
            '.style-scope.ytcp-social-suggestions-textbox[aria-label*="viewers"]',  # 类名 + aria-label
            '[aria-label="Description"]',  # 备用选择器
            '[aria-label*="Description"]',  # 部分匹配备用
            '#description-textarea',  # 最后的备用选择器
        ],

        # 缩略图上传
        "thumbnail_input": [
            'input[accept*="image"]',
            '#thumbnail-input',
            '[data-testid="thumbnail-input"]',
            'input[type="file"][accept*="jpg,jpeg,png"]',
        ],

        # 标签输入框
        "tags_input": [
            '[aria-label="Tags"]',
            '#tags-input',
            'input[placeholder*="tag"]',
            'textarea[aria-label="Tags"]',
        ],

        # 显示更多按钮
        "more_options_button": [
            'button:has-text("Show more")',
            'button:has-text("显示更多")',
            '[aria-label="Show more"]',
            'button:has-text("MORE OPTIONS")',
            '#more-options-button',
        ],
    }
    
    # 设置页面选择器 (基于Playwright实际检查结果更新)
    SETTINGS_SELECTORS = {
        # 播放列表下拉框 (基于实际界面检查)
        "playlist_dropdown": [
            '*:has-text("Select")',  # 实际检查发现的Select下拉框
            'div:has-text("Playlists") + * [role="button"]',  # Playlists标签后的按钮
            '[aria-label="Select playlist"]',
            'tp-yt-paper-dropdown-menu[label*="playlist"]',
        ],

        # 播放列表选项
        "playlist_option": [
            '[role="option"]',  # 实际检查发现的选项
            '[role="menuitem"]',
            'tp-yt-paper-item',
        ],

        # 受众设置 - Made for kids (基于实际界面检查)
        "audience_kids_yes": [
            'tp-yt-paper-radio-button:has-text("Yes, it\'s made for kids")',  # 实际检查发现的文本
            'tp-yt-paper-radio-button[name="VIDEO_MADE_FOR_KIDS_MFK"]',
            'input[name="audience"][value="kids"]',
        ],

        "audience_kids_no": [
            'tp-yt-paper-radio-button:has-text("No, it\'s not made for kids")',  # 实际检查发现的文本
            'tp-yt-paper-radio-button[name="VIDEO_MADE_FOR_KIDS_NOT_MFK"]',
            'input[name="audience"][value="not_kids"]',
        ],

        # 年龄限制 (基于实际界面检查)
        "age_restriction_expand": [
            '*:has-text("Age restriction (advanced)")',  # 实际检查发现的展开按钮
            'button:has-text("Age restriction (advanced)")',
            '[aria-label*="age restriction"]',
        ],

        "age_restriction_yes": [
            'tp-yt-paper-radio-button:has-text("Yes, restrict my video to viewers over 18")',  # 实际检查发现的文本
            'tp-yt-paper-checkbox[aria-label*="age restriction"]',
            'input[type="checkbox"][aria-label*="18+"]',
        ],

        "age_restriction_no": [
            'tp-yt-paper-radio-button:has-text("No, don\'t restrict my video to viewers over 18 only")',  # 实际检查发现的文本
            'input[type="radio"][value="no_restriction"]',
        ],

        # 语言设置
        "language_dropdown": [
            '[aria-label="Video language"]',
            'tp-yt-paper-dropdown-menu[label*="language"]',
        ],
    }
    
    # 可见性设置选择器 (基于MCP分析结果更新)
    VISIBILITY_SELECTORS = {
        "visibility_section": [
            '[aria-label="Visibility"]',
            'div:has-text("Visibility")',
            'text="Visibility"',
        ],

        # 公开选项 (已验证有效)
        "public_option": [
            'text="Public"',  # 最准确的选择器
            'tp-yt-paper-radio-button:has-text("Public")',
            '[aria-label*="Public"]',
            'tp-yt-paper-radio-button[name="PUBLIC"]',
            'input[value="PUBLIC"]',
        ],

        # 未列出选项 (已验证有效)
        "unlisted_option": [
            'text="Unlisted"',  # 最准确的选择器
            'tp-yt-paper-radio-button:has-text("Unlisted")',
            '[aria-label*="Unlisted"]',
            'tp-yt-paper-radio-button[name="UNLISTED"]',
            'input[value="UNLISTED"]',
        ],

        # 私密选项 (已验证有效)
        "private_option": [
            'text="Private"',  # 最准确的选择器
            'tp-yt-paper-radio-button:has-text("Private")',
            '[aria-label*="Private"]',
            'tp-yt-paper-radio-button[name="PRIVATE"]',
            'input[value="PRIVATE"]',
        ],

        # 定时发布选项 (已验证有效)
        "scheduled_option": [
            'text="Scheduled"',  # 最准确的选择器
            'tp-yt-paper-radio-button:has-text("Scheduled")',
            '[aria-label*="Scheduled"]',
            'tp-yt-paper-radio-button[name="SCHEDULE"]',
            'input[value="SCHEDULE"]',
        ],

        # 定时发布日期时间选择器
        "schedule_date_input": [
            'input[aria-label*="date"]',
            'input[type="date"]',
            '[data-testid="date-input"]',
        ],

        "schedule_time_input": [
            'input[aria-label*="time"]',
            'input[type="time"]',
            '[data-testid="time-input"]',
        ],
    }
    
    # 发布按钮选择器
    PUBLISH_SELECTORS = {
        "publish_button": [
            'button:has-text("Publish")',
            'button:has-text("发布")',
            '[aria-label="Publish"]',
            '#publish-button',
            'ytcp-button:has-text("Publish")',
        ],

        "publish_success": [
            'div:has-text("Video published")',
            'div:has-text("视频已发布")',
            '.publish-success',
            '[aria-label*="published"]',
            'div:has-text("Your video is live")',
        ],

        "video_link": [
            'a[href*="youtube.com/watch"]',
            'a[href*="youtu.be"]',
            '[data-testid="video-link"]',
            '.video-url-link',
        ],

        "done_button": [
            'button:has-text("Done")',
            'button:has-text("完成")',
            '[aria-label="Done"]',
            '#done-button',
        ],
    }

    # 导航按钮选择器 (基于Playwright实际检查结果更新)
    NAVIGATION_SELECTORS = {
        "next_button": [
            'ytcp-button:has-text("Next")',  # 实际检查发现的最可靠选择器
            'button:has-text("Next")',  # 通用按钮选择器
            'ytcp-button-shape:has-text("Next")',  # 实际检查发现的按钮形状
            'button[aria-label="Next"]',
            '#next-button',
            '.next-button',
        ],

        "back_button": [
            'button:has-text("Back")',
            'button[aria-label="Back"]',
            '#back-button',
            '.back-button',
        ],

        "continue_button": [
            'button:has-text("Continue")',
            'button:has-text("继续")',
            'button[aria-label="Continue"]',
            '#continue-button',
        ],
    }
    
    # 进度和状态选择器
    PROGRESS_SELECTORS = {
        "upload_progress": [
            '[aria-label*="upload progress"]',
            '.upload-progress',
            'tp-yt-paper-progress',
        ],
        
        "processing_status": [
            'div:has-text("Processing")',
            'div:has-text("处理中")',
            '.processing-status',
        ],
        
        "upload_complete": [
            'div:has-text("Upload complete")',
            'div:has-text("上传完成")',
            '.upload-complete',
        ],
    }
    
    # YPP 广告适合度选择器
    YPP_SELECTORS = {
        "ypp_section": [
            'div:has-text("Advertiser-friendly content")',
            'div:has-text("广告客户友好内容")',
            '[aria-label*="Advertiser-friendly"]',
            '#ypp-section',
        ],

        "ypp_none_option": [
            'tp-yt-paper-radio-button:has-text("None of the above")',
            'tp-yt-paper-radio-button:has-text("以上都没有")',
            'input[value="NONE_OF_THE_ABOVE"]',
            '[aria-label*="None of the above"]',
        ],

        "ypp_continue_button": [
            'button:has-text("Continue")',
            'button:has-text("继续")',
            'button:has-text("Submit")',
            'button:has-text("提交")',
            '[aria-label="Continue"]',
        ],
    }

    # 受众和限制选择器 (基于MCP分析结果更新)
    AUDIENCE_SELECTORS = {
        # 儿童内容设置 (已验证有效)
        "audience_made_for_kids_yes": [
            'text="Yes, it\'s made for kids"',  # 最准确的选择器
            'tp-yt-paper-radio-button:has-text("Yes")',  # 通用Yes选择器
            'tp-yt-paper-radio-button[name="VIDEO_MADE_FOR_KIDS_MFK"]',  # 备用
            '[aria-label*="made for kids"][value="true"]',
        ],

        "audience_made_for_kids_no": [
            'text="No, it\'s not made for kids"',  # 最准确的选择器
            'tp-yt-paper-radio-button:has-text("No")',  # 通用No选择器
            'tp-yt-paper-radio-button[name="VIDEO_MADE_FOR_KIDS_NOT_MFK"]',  # 备用
            '[aria-label*="not made for kids"]',
        ],

        # 年龄限制设置 (基于截图分析)
        "age_restriction_yes": [
            'text="Yes, restrict my video to viewers over 18"',  # 最准确的选择器
            'tp-yt-paper-radio-button:has-text("Yes, restrict")',
            '[aria-label*="restrict"][aria-label*="18"]',
            'tp-yt-paper-checkbox[aria-label*="age restriction"]',
        ],

        "age_restriction_no": [
            'text="No, don\'t restrict my video to viewers over 18 only"',  # 最准确的选择器
            'tp-yt-paper-radio-button:has-text("No, don\'t restrict")',
            '[aria-label*="don\'t restrict"]',
            '[aria-label*="No restriction"]',
        ],

        # Shorts混剪设置 (基于截图分析)
        "remix_allow_video_audio": [
            'text="Allow video and audio remixing"',  # 最准确的选择器
            'tp-yt-paper-radio-button:has-text("Allow video and audio remixing")',
            '[aria-label*="video and audio remixing"]',
            'input[value="ALLOW_VIDEO_AND_AUDIO_REMIXING"]',
        ],

        "remix_allow_audio_only": [
            'text="Allow only audio remixing"',  # 最准确的选择器
            'tp-yt-paper-radio-button:has-text("Allow only audio remixing")',
            '[aria-label*="only audio remixing"]',
            'input[value="ALLOW_AUDIO_ONLY_REMIXING"]',
        ],

        # Altered content设置 (基于截图分析)
        "altered_content_yes": [
            'text="Yes"',  # 需要在Altered content上下文中使用
            'tp-yt-paper-radio-button:has-text("Yes")',
            '[aria-label*="altered content"][value="yes"]',
        ],

        "altered_content_no": [
            'text="No"',  # 需要在Altered content上下文中使用
            'tp-yt-paper-radio-button:has-text("No")',
            '[aria-label*="altered content"][value="no"]',
        ],
    }
    
    @classmethod
    def get_selector(cls, category: str, element: str) -> List[str]:
        """
        获取指定元素的选择器列表
        
        Args:
            category: 选择器分类
            element: 元素名称
            
        Returns:
            List[str]: 选择器列表
        """
        selectors_dict = getattr(cls, f"{category.upper()}_SELECTORS", {})
        return selectors_dict.get(element, [])
    
    @classmethod
    def get_all_selectors_for_element(cls, element: str) -> List[str]:
        """
        获取指定元素在所有分类中的选择器
        
        Args:
            element: 元素名称
            
        Returns:
            List[str]: 所有可能的选择器列表
        """
        all_selectors = []
        
        # 遍历所有选择器分类
        for attr_name in dir(cls):
            if attr_name.endswith('_SELECTORS'):
                selectors_dict = getattr(cls, attr_name)
                if element in selectors_dict:
                    all_selectors.extend(selectors_dict[element])
        
        return all_selectors
