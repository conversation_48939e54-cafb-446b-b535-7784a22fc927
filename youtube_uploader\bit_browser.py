"""
比特浏览器集成模块

基于现有 bit_api.py，封装比特浏览器操作，包括 profile 检查、浏览器启动、WebSocket 获取等。
"""

import sys
import os
import time
import asyncio
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple
from enum import Enum
from datetime import datetime, timedelta
import logging
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# 添加项目根目录到 Python 路径，以便导入 bit_api
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from bit_api import BitBrowser as BaseBitBrowser
except ImportError as e:
    logging.error(f"无法导入 bit_api: {e}")
    raise


# 异常类定义
class BitBrowserError(Exception):
    """比特浏览器相关错误基类"""
    pass


class ProfileNotFoundError(BitBrowserError):
    """Profile不存在错误"""
    pass


class BrowserOpenError(BitBrowserError):
    """浏览器打开失败错误"""
    pass


class BrowserCloseError(BitBrowserError):
    """浏览器关闭失败错误"""
    pass


class ConnectionError(BitBrowserError):
    """连接错误"""
    pass


# 枚举定义
class BrowserStatus(str, Enum):
    """浏览器状态"""
    CLOSED = "closed"
    OPENING = "opening"
    OPEN = "open"
    CLOSING = "closing"
    ERROR = "error"


class HealthStatus(str, Enum):
    """健康状态"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class BitBrowserManager:
    """比特浏览器管理器"""

    def __init__(self, website: str = "YouTube", max_retries: int = 3, retry_delay: float = 2.0):
        """
        初始化比特浏览器管理器

        Args:
            website: 网站名称，用于浏览器窗口标识
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.website = website
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # 初始化比特浏览器API
        try:
            self.bit_browser = BaseBitBrowser(website)
            self.logger.info(f"比特浏览器管理器初始化成功，网站: {website}")
        except Exception as e:
            self.logger.error(f"比特浏览器管理器初始化失败: {e}")
            raise BitBrowserError(f"初始化失败: {e}") from e

        # 状态管理
        self._active_browsers: Dict[str, str] = {}  # profile_name -> browser_id
        self._active_ws: Dict[str, str] = {}  # browser_id -> ws_address
        self._browser_status: Dict[str, BrowserStatus] = {}  # profile_name -> status
        self._last_health_check: Dict[str, datetime] = {}  # profile_name -> last_check_time
        self._connection_attempts: Dict[str, int] = {}  # profile_name -> attempt_count

    def _update_browser_status(self, profile_name: str, status: BrowserStatus):
        """更新浏览器状态"""
        self._browser_status[profile_name] = status
        self.logger.debug(f"Profile '{profile_name}' 状态更新为: {status}")

    def get_browser_status(self, profile_name: str) -> BrowserStatus:
        """获取浏览器状态"""
        return self._browser_status.get(profile_name, BrowserStatus.CLOSED)

    def is_bit_browser_running(self) -> bool:
        """检查比特浏览器是否运行"""
        try:
            port = self.bit_browser.get_bit_port()
            return self.bit_browser.test_port(port)
        except Exception as e:
            self.logger.error(f"检查比特浏览器运行状态失败: {e}")
            return False

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((ConnectionError, TimeoutError))
    )
    def _check_browser_health(self, profile_name: str) -> HealthStatus:
        """检查浏览器健康状态"""
        try:
            browser_id = self.get_browser_id(profile_name)
            if not browser_id:
                return HealthStatus.UNHEALTHY

            # 获取浏览器详情来检查健康状态
            details = self.bit_browser.browser_detail(browser_id)
            if details:
                self._last_health_check[profile_name] = datetime.now()
                return HealthStatus.HEALTHY
            else:
                return HealthStatus.UNHEALTHY

        except Exception as e:
            self.logger.warning(f"检查 profile '{profile_name}' 健康状态失败: {e}")
            return HealthStatus.UNKNOWN

    def get_health_status(self, profile_name: str) -> HealthStatus:
        """获取浏览器健康状态"""
        # 如果最近5分钟内检查过，直接返回缓存结果
        last_check = self._last_health_check.get(profile_name)
        if last_check and datetime.now() - last_check < timedelta(minutes=5):
            return HealthStatus.HEALTHY

        return self._check_browser_health(profile_name)

    def wait_for_browser_ready(self, profile_name: str, timeout: int = 30) -> bool:
        """等待浏览器准备就绪"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            status = self.get_browser_status(profile_name)
            if status == BrowserStatus.OPEN:
                health = self.get_health_status(profile_name)
                if health == HealthStatus.HEALTHY:
                    return True
            elif status == BrowserStatus.ERROR:
                return False

            time.sleep(1)

        self.logger.warning(f"等待 profile '{profile_name}' 准备就绪超时")
        return False
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type((ConnectionError, TimeoutError))
    )
    def check_profile_exists(self, profile_name: str) -> bool:
        """
        检查指定的 profile 是否存在

        Args:
            profile_name: profile 名称

        Returns:
            bool: profile 是否存在
        """
        try:
            # 首先检查比特浏览器是否运行
            if not self.is_bit_browser_running():
                raise ConnectionError("比特浏览器未运行或无法连接")

            browsers = self.bit_browser.browser_list(username=profile_name)
            exists = len(browsers) > 0

            if exists:
                self.logger.info(f"Profile '{profile_name}' 存在，浏览器数量: {len(browsers)}")
            else:
                self.logger.warning(f"Profile '{profile_name}' 不存在")

            return exists

        except Exception as e:
            self.logger.error(f"检查 profile '{profile_name}' 时出错: {e}")
            raise BitBrowserError(f"检查 profile 失败: {e}") from e

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type((ConnectionError, TimeoutError))
    )
    def get_browser_id(self, profile_name: str) -> Optional[str]:
        """
        获取指定 profile 的浏览器 ID

        Args:
            profile_name: profile 名称

        Returns:
            Optional[str]: 浏览器 ID，如果不存在则返回 None

        Raises:
            ProfileNotFoundError: 当 profile 不存在时
        """
        try:
            browsers = self.bit_browser.browser_list(username=profile_name)

            if not browsers:
                raise ProfileNotFoundError(f"Profile '{profile_name}' 不存在")

            browser_id = browsers[0]['id']
            self.logger.debug(f"获取到 profile '{profile_name}' 的浏览器 ID: {browser_id}")
            return browser_id

        except ProfileNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"获取 profile '{profile_name}' 浏览器 ID 时出错: {e}")
            raise BitBrowserError(f"获取浏览器 ID 失败: {e}") from e

    def get_browser_info(self, profile_name: str) -> Optional[Dict[str, Any]]:
        """
        获取浏览器完整信息

        Args:
            profile_name: profile 名称

        Returns:
            Optional[Dict[str, Any]]: 浏览器信息字典
        """
        try:
            browsers = self.bit_browser.browser_list(username=profile_name)
            if browsers:
                browser_info = browsers[0]
                browser_info['health_status'] = self.get_health_status(profile_name)
                browser_info['current_status'] = self.get_browser_status(profile_name)
                browser_info['is_active'] = profile_name in self._active_browsers
                return browser_info
            return None

        except Exception as e:
            self.logger.error(f"获取 profile '{profile_name}' 浏览器信息时出错: {e}")
            return None
    
    def open_browser(self, profile_name: str, wait_ready: bool = True, timeout: int = 30) -> Optional[str]:
        """
        打开指定 profile 的浏览器并获取 WebSocket 地址

        Args:
            profile_name: profile 名称
            wait_ready: 是否等待浏览器准备就绪
            timeout: 超时时间（秒）

        Returns:
            Optional[str]: WebSocket 调试地址，失败时返回 None

        Raises:
            ProfileNotFoundError: 当 profile 不存在时
            BrowserOpenError: 当浏览器打开失败时
        """
        try:
            # 检查是否已经打开
            if profile_name in self._active_browsers:
                existing_ws = self._active_ws.get(self._active_browsers[profile_name])
                if existing_ws:
                    self.logger.info(f"Profile '{profile_name}' 的浏览器已经打开，WebSocket: {existing_ws}")
                    return existing_ws

            # 更新状态为正在打开
            self._update_browser_status(profile_name, BrowserStatus.OPENING)

            # 重置连接尝试计数
            self._connection_attempts[profile_name] = 0

            # 获取浏览器 ID
            browser_id = self.get_browser_id(profile_name)

            # 尝试打开浏览器
            for attempt in range(self.max_retries):
                try:
                    self._connection_attempts[profile_name] = attempt + 1
                    self.logger.info(f"正在打开 profile '{profile_name}' 的浏览器... (尝试 {attempt + 1}/{self.max_retries})")

                    ws_address = self.bit_browser.open_browser(browser_id)

                    if ws_address:
                        # 记录活动状态
                        self._active_browsers[profile_name] = browser_id
                        self._active_ws[browser_id] = ws_address
                        self._update_browser_status(profile_name, BrowserStatus.OPEN)

                        self.logger.info(f"成功打开 profile '{profile_name}' 的浏览器，WebSocket: {ws_address}")

                        # 等待浏览器准备就绪
                        if wait_ready:
                            if self.wait_for_browser_ready(profile_name, timeout):
                                return ws_address
                            else:
                                self.logger.warning(f"Profile '{profile_name}' 浏览器未能在 {timeout} 秒内准备就绪")
                                # 继续返回WebSocket地址，让调用者决定如何处理

                        return ws_address
                    else:
                        self.logger.warning(f"打开 profile '{profile_name}' 的浏览器失败 (尝试 {attempt + 1})")

                except Exception as e:
                    self.logger.warning(f"打开 profile '{profile_name}' 浏览器时出错 (尝试 {attempt + 1}): {e}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)

            # 所有尝试都失败
            self._update_browser_status(profile_name, BrowserStatus.ERROR)
            error_msg = f"打开 profile '{profile_name}' 的浏览器失败，已尝试 {self.max_retries} 次"
            self.logger.error(error_msg)
            raise BrowserOpenError(error_msg)

        except ProfileNotFoundError:
            self._update_browser_status(profile_name, BrowserStatus.ERROR)
            raise
        except BrowserOpenError:
            raise
        except Exception as e:
            self._update_browser_status(profile_name, BrowserStatus.ERROR)
            error_msg = f"打开 profile '{profile_name}' 浏览器时发生未知错误: {e}"
            self.logger.error(error_msg)
            raise BrowserOpenError(error_msg) from e
    
    def close_browser(self, profile_name: str, force: bool = False) -> bool:
        """
        关闭指定 profile 的浏览器

        Args:
            profile_name: profile 名称
            force: 是否强制关闭（即使API调用失败也清理本地状态）

        Returns:
            bool: 是否成功关闭
        """
        try:
            browser_id = self._active_browsers.get(profile_name)
            if not browser_id:
                self.logger.info(f"Profile '{profile_name}' 的浏览器未在活动列表中，可能已经关闭")
                self._update_browser_status(profile_name, BrowserStatus.CLOSED)
                return True

            # 更新状态为正在关闭
            self._update_browser_status(profile_name, BrowserStatus.CLOSING)

            # 尝试关闭浏览器
            success = False
            for attempt in range(self.max_retries):
                try:
                    self.logger.info(f"正在关闭 profile '{profile_name}' 的浏览器... (尝试 {attempt + 1}/{self.max_retries})")
                    success = self.bit_browser.close_browser(browser_id)

                    if success:
                        break
                    else:
                        self.logger.warning(f"关闭 profile '{profile_name}' 的浏览器失败 (尝试 {attempt + 1})")

                except Exception as e:
                    self.logger.warning(f"关闭 profile '{profile_name}' 浏览器时出错 (尝试 {attempt + 1}): {e}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)

            # 清理本地状态
            if success or force:
                self._active_browsers.pop(profile_name, None)
                self._active_ws.pop(browser_id, None)
                self._browser_status.pop(profile_name, None)
                self._last_health_check.pop(profile_name, None)
                self._connection_attempts.pop(profile_name, None)
                self._update_browser_status(profile_name, BrowserStatus.CLOSED)

                if success:
                    self.logger.info(f"成功关闭 profile '{profile_name}' 的浏览器")
                else:
                    self.logger.warning(f"强制清理 profile '{profile_name}' 的本地状态")
            else:
                self._update_browser_status(profile_name, BrowserStatus.ERROR)
                self.logger.error(f"关闭 profile '{profile_name}' 的浏览器失败")

            return success or force

        except Exception as e:
            self.logger.error(f"关闭 profile '{profile_name}' 浏览器时发生未知错误: {e}")

            # 如果强制关闭，清理本地状态
            if force:
                self._active_browsers.pop(profile_name, None)
                if browser_id:
                    self._active_ws.pop(browser_id, None)
                self._browser_status.pop(profile_name, None)
                self._last_health_check.pop(profile_name, None)
                self._connection_attempts.pop(profile_name, None)
                return True

            return False
    
    def get_active_profiles(self) -> List[str]:
        """
        获取当前活动的 profile 列表

        Returns:
            List[str]: 活动的 profile 名称列表
        """
        return list(self._active_browsers.keys())

    def get_all_profiles_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有已知 profile 的状态

        Returns:
            Dict[str, Dict[str, Any]]: profile状态字典
        """
        status_dict = {}

        # 获取活动的profiles
        for profile_name in self._active_browsers.keys():
            status_dict[profile_name] = {
                'status': self.get_browser_status(profile_name),
                'health': self.get_health_status(profile_name),
                'browser_id': self._active_browsers[profile_name],
                'ws_address': self._active_ws.get(self._active_browsers[profile_name]),
                'connection_attempts': self._connection_attempts.get(profile_name, 0),
                'last_health_check': self._last_health_check.get(profile_name)
            }

        return status_dict

    def cleanup_inactive_browsers(self) -> int:
        """
        清理不活跃的浏览器连接

        Returns:
            int: 清理的浏览器数量
        """
        cleaned_count = 0
        inactive_profiles = []

        for profile_name in list(self._active_browsers.keys()):
            health = self.get_health_status(profile_name)
            if health == HealthStatus.UNHEALTHY:
                inactive_profiles.append(profile_name)

        for profile_name in inactive_profiles:
            self.logger.info(f"清理不活跃的浏览器: {profile_name}")
            if self.close_browser(profile_name, force=True):
                cleaned_count += 1

        return cleaned_count

    def close_all_browsers(self, force: bool = False) -> Tuple[int, int]:
        """
        关闭所有活动的浏览器

        Args:
            force: 是否强制关闭

        Returns:
            Tuple[int, int]: (成功关闭数量, 总数量)
        """
        active_profiles = list(self._active_browsers.keys())
        total_count = len(active_profiles)
        success_count = 0

        self.logger.info(f"开始关闭 {total_count} 个活动浏览器...")

        for profile_name in active_profiles:
            try:
                if self.close_browser(profile_name, force=force):
                    success_count += 1
            except Exception as e:
                self.logger.error(f"关闭 profile '{profile_name}' 时出错: {e}")

        self.logger.info(f"浏览器关闭完成: {success_count}/{total_count}")
        return success_count, total_count

    def restart_browser(self, profile_name: str, timeout: int = 30) -> Optional[str]:
        """
        重启指定 profile 的浏览器

        Args:
            profile_name: profile 名称
            timeout: 超时时间（秒）

        Returns:
            Optional[str]: 新的 WebSocket 地址
        """
        self.logger.info(f"重启 profile '{profile_name}' 的浏览器...")

        # 先关闭
        self.close_browser(profile_name, force=True)

        # 等待一下
        time.sleep(2)

        # 重新打开
        return self.open_browser(profile_name, wait_ready=True, timeout=timeout)
    
    def get_browser_details(self, profile_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定 profile 的浏览器详细信息

        Args:
            profile_name: profile 名称

        Returns:
            Optional[Dict[str, Any]]: 浏览器详细信息，失败时返回 None
        """
        try:
            browser_id = self.get_browser_id(profile_name)
            if not browser_id:
                return None

            details = self.bit_browser.browser_detail(browser_id)

            # 添加运行时状态信息
            if details:
                details['runtime_status'] = {
                    'status': self.get_browser_status(profile_name),
                    'health': self.get_health_status(profile_name),
                    'is_active': profile_name in self._active_browsers,
                    'ws_address': self._active_ws.get(browser_id),
                    'connection_attempts': self._connection_attempts.get(profile_name, 0),
                    'last_health_check': self._last_health_check.get(profile_name)
                }

            return details

        except Exception as e:
            self.logger.error(f"获取 profile '{profile_name}' 浏览器详情时出错: {e}")
            return None

    def get_manager_stats(self) -> Dict[str, Any]:
        """
        获取管理器统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        active_count = len(self._active_browsers)
        status_counts = {}
        health_counts = {}

        # 统计状态分布
        for profile_name in self._active_browsers.keys():
            status = self.get_browser_status(profile_name)
            health = self.get_health_status(profile_name)

            status_counts[status] = status_counts.get(status, 0) + 1
            health_counts[health] = health_counts.get(health, 0) + 1

        return {
            'active_browsers': active_count,
            'bit_browser_running': self.is_bit_browser_running(),
            'status_distribution': status_counts,
            'health_distribution': health_counts,
            'total_connection_attempts': sum(self._connection_attempts.values()),
            'manager_config': {
                'website': self.website,
                'max_retries': self.max_retries,
                'retry_delay': self.retry_delay
            }
        }

    def print_status_summary(self):
        """打印状态摘要"""
        stats = self.get_manager_stats()

        print("=" * 60)
        print("比特浏览器管理器状态摘要")
        print("=" * 60)
        print(f"比特浏览器运行状态: {'✅ 运行中' if stats['bit_browser_running'] else '❌ 未运行'}")
        print(f"活动浏览器数量: {stats['active_browsers']}")
        print(f"总连接尝试次数: {stats['total_connection_attempts']}")

        if stats['status_distribution']:
            print("\n状态分布:")
            for status, count in stats['status_distribution'].items():
                print(f"  {status}: {count}")

        if stats['health_distribution']:
            print("\n健康状态分布:")
            for health, count in stats['health_distribution'].items():
                print(f"  {health}: {count}")

        print(f"\n管理器配置:")
        config = stats['manager_config']
        print(f"  网站: {config['website']}")
        print(f"  最大重试次数: {config['max_retries']}")
        print(f"  重试延迟: {config['retry_delay']}秒")

        # 显示活动浏览器详情
        if stats['active_browsers'] > 0:
            print(f"\n活动浏览器详情:")
            all_status = self.get_all_profiles_status()
            for profile_name, status_info in all_status.items():
                status_icon = {
                    BrowserStatus.OPEN: "✅",
                    BrowserStatus.OPENING: "🔄",
                    BrowserStatus.CLOSING: "🔄",
                    BrowserStatus.CLOSED: "❌",
                    BrowserStatus.ERROR: "⚠️"
                }.get(status_info['status'], "❓")

                health_icon = {
                    HealthStatus.HEALTHY: "💚",
                    HealthStatus.UNHEALTHY: "❤️",
                    HealthStatus.UNKNOWN: "💛"
                }.get(status_info['health'], "❓")

                print(f"  {profile_name}: {status_icon} {status_info['status']} | {health_icon} {status_info['health']}")

        print("=" * 60)
