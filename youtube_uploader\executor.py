"""
任务执行引擎

实现任务调度、并发控制、错误处理、重试机制等核心执行逻辑。
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
import logging
from concurrent.futures import ThreadPoolExecutor

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

from .models import TaskModel, ResultModel, TaskStatus
from .bit_browser import BitBrowserManager
from .youtube_automation import YouTubeAutomation
from .utils import random_delay, log_step


class TaskExecutor:
    """任务执行器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化任务执行器
        
        Args:
            config: 配置字典
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        self.bit_browser_manager = BitBrowserManager()
        self.youtube_automation = YouTubeAutomation(config)
        self.results: List[ResultModel] = []
    
    async def execute_tasks(self, tasks: List[TaskModel]) -> List[ResultModel]:
        """
        执行任务列表
        
        Args:
            tasks: 任务列表
            
        Returns:
            List[ResultModel]: 执行结果列表
        """
        self.logger.info(f"开始执行 {len(tasks)} 个任务")
        start_time = time.time()
        
        # 按 profile 分组任务
        profile_tasks = self._group_tasks_by_profile(tasks)
        
        # 根据并发设置执行任务
        concurrency = self.config.get('concurrency', 1)
        
        if concurrency == 1:
            # 串行执行
            await self._execute_sequential(profile_tasks)
        else:
            # 并发执行
            await self._execute_concurrent(profile_tasks, concurrency)
        
        total_time = time.time() - start_time
        self.logger.info(f"所有任务执行完成，总耗时: {total_time:.1f}秒")
        
        return self.results
    
    def _group_tasks_by_profile(self, tasks: List[TaskModel]) -> Dict[str, List[TaskModel]]:
        """按 profile 分组任务"""
        profile_tasks = {}
        for task in tasks:
            if task.profile_name not in profile_tasks:
                profile_tasks[task.profile_name] = []
            profile_tasks[task.profile_name].append(task)
        
        self.logger.info(f"任务分组完成，共 {len(profile_tasks)} 个 profile")
        return profile_tasks
    
    async def _execute_sequential(self, profile_tasks: Dict[str, List[TaskModel]]):
        """串行执行任务"""
        self.logger.info("使用串行模式执行任务")
        
        for profile_name, tasks in profile_tasks.items():
            await self._execute_profile_tasks(profile_name, tasks)
    
    async def _execute_concurrent(self, profile_tasks: Dict[str, List[TaskModel]], concurrency: int):
        """并发执行任务"""
        self.logger.info(f"使用并发模式执行任务，并发数: {concurrency}")
        
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(concurrency)
        
        # 创建任务协程
        coroutines = []
        for profile_name, tasks in profile_tasks.items():
            coroutine = self._execute_profile_tasks_with_semaphore(semaphore, profile_name, tasks)
            coroutines.append(coroutine)
        
        # 并发执行
        await asyncio.gather(*coroutines, return_exceptions=True)
    
    async def _execute_profile_tasks_with_semaphore(self, semaphore: asyncio.Semaphore, 
                                                   profile_name: str, tasks: List[TaskModel]):
        """带信号量的 profile 任务执行"""
        async with semaphore:
            await self._execute_profile_tasks(profile_name, tasks)
    
    async def _execute_profile_tasks(self, profile_name: str, tasks: List[TaskModel]):
        """执行单个 profile 的所有任务"""
        self.logger.info(f"开始执行 profile '{profile_name}' 的 {len(tasks)} 个任务")
        
        # 检查 profile 是否存在
        if not self.bit_browser_manager.check_profile_exists(profile_name):
            self.logger.error(f"Profile '{profile_name}' 不存在，跳过相关任务")
            self._mark_tasks_failed(tasks, f"Profile '{profile_name}' 不存在")
            return
        
        # 打开浏览器
        ws_address = self.bit_browser_manager.open_browser(profile_name)
        if not ws_address:
            self.logger.error(f"无法打开 profile '{profile_name}' 的浏览器，跳过相关任务")
            self._mark_tasks_failed(tasks, f"无法打开浏览器")
            return
        
        try:
            # 连接到浏览器
            async with async_playwright() as p:
                browser = await p.chromium.connect_over_cdp(ws_address)
                
                try:
                    # 获取默认上下文
                    context = browser.contexts[0] if browser.contexts else await browser.new_context()
                    
                    # 执行每个任务
                    for i, task in enumerate(tasks):
                        try:
                            # 创建新的标签页
                            page = await context.new_page()
                            
                            try:
                                # 执行任务
                                result = await self._execute_single_task(page, task)
                                self.results.append(result)
                                
                                # 任务间隔
                                if i < len(tasks) - 1:  # 不是最后一个任务
                                    interval = self.config.get('operation_interval_seconds', 10)
                                    self.logger.info(f"等待 {interval} 秒后执行下一个任务...")
                                    await asyncio.sleep(interval)
                                    random_delay(
                                        self.config.get('random_delay_min', 2),
                                        self.config.get('random_delay_max', 5)
                                    )
                                
                            finally:
                                # 关闭标签页
                                await page.close()
                                
                        except Exception as e:
                            self.logger.error(f"执行任务时出错: {e}")
                            # 创建失败结果
                            result = self._create_failed_result(task, str(e))
                            self.results.append(result)
                
                finally:
                    # 断开浏览器连接（不关闭浏览器）
                    await browser.close()
        
        finally:
            # 关闭比特浏览器（可选，根据需要决定是否关闭）
            # self.bit_browser_manager.close_browser(profile_name)
            pass
    
    async def _execute_single_task(self, page: Page, task: TaskModel) -> ResultModel:
        """执行单个任务"""
        max_attempts = self.config.get('max_retry_attempts', 3)
        retry_delay = self.config.get('retry_delay_seconds', 3)
        
        for attempt in range(max_attempts):
            try:
                if attempt > 0:
                    self.logger.info(f"重试第 {attempt} 次: {task.profile_name} - {task.title}")
                    await asyncio.sleep(retry_delay)
                
                result = await self.youtube_automation.upload_video(page, task)
                
                if result.status == TaskStatus.SUCCESS:
                    return result
                elif result.status == TaskStatus.NEEDS_VERIFICATION:
                    # 需要人工验证，不重试
                    return result
                else:
                    # 失败，继续重试
                    if attempt == max_attempts - 1:
                        return result
                    
            except Exception as e:
                self.logger.error(f"任务执行异常 (尝试 {attempt + 1}/{max_attempts}): {e}")
                if attempt == max_attempts - 1:
                    return self._create_failed_result(task, str(e))
        
        # 理论上不会到达这里
        return self._create_failed_result(task, "未知错误")
    
    def _mark_tasks_failed(self, tasks: List[TaskModel], error_message: str):
        """标记任务为失败"""
        for task in tasks:
            result = self._create_failed_result(task, error_message)
            self.results.append(result)
    
    def _create_failed_result(self, task: TaskModel, error_message: str) -> ResultModel:
        """创建失败结果"""
        from datetime import datetime
        now = datetime.now()
        return ResultModel(
            task_id=f"failed_{int(time.time())}",
            profile_name=task.profile_name,
            video_path=task.video_path,
            status=TaskStatus.FAILED,
            error_message=error_message,
            started_at=now,
            finished_at=now,
            duration_seconds=0.0
        )
