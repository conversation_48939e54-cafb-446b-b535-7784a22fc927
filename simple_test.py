#!/usr/bin/env python3
"""
简单测试脚本
验证系统是否正常工作
"""

import sys
from pathlib import Path

print("🧪 简单系统测试...")

try:
    # 测试路径
    print(f"当前目录: {Path.cwd()}")
    print(f"Python路径: {sys.executable}")
    
    # 测试文件存在
    files_to_check = [
        'tasks_template.xlsx',
        'youtube_uploader/__init__.py',
        'youtube_uploader/models.py',
        'youtube_uploader/selectors.py',
        'youtube_uploader/excel_handler.py',
        'youtube_uploader/youtube_automation.py',
        'youtube_uploader/batch_uploader.py',
        'bit_api.py'
    ]
    
    print(f"\n📁 检查文件:")
    for file_path in files_to_check:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
    
    # 测试导入
    print(f"\n📦 测试导入:")
    
    try:
        sys.path.insert(0, str(Path.cwd()))
        from youtube_uploader.models import TaskModel
        print(f"  ✅ TaskModel")
    except Exception as e:
        print(f"  ❌ TaskModel: {e}")
    
    try:
        from youtube_uploader.selectors import YouTubeSelectors
        print(f"  ✅ YouTubeSelectors")
    except Exception as e:
        print(f"  ❌ YouTubeSelectors: {e}")
    
    try:
        from youtube_uploader.excel_handler import ExcelHandler
        print(f"  ✅ ExcelHandler")
    except Exception as e:
        print(f"  ❌ ExcelHandler: {e}")
    
    try:
        from bit_api import BitBrowser
        print(f"  ✅ BitBrowser")
    except Exception as e:
        print(f"  ❌ BitBrowser: {e}")
    
    # 测试Excel读取
    print(f"\n📊 测试Excel读取:")
    try:
        from youtube_uploader.excel_handler import ExcelHandler
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        if tasks:
            print(f"  ✅ 读取到 {len(tasks)} 个任务")
            task = tasks[0]
            print(f"    任务1: {task.title}")
            print(f"    Profile: {task.profile_name}")
            print(f"    视频路径: {task.video_path}")
        else:
            print(f"  ⚠️ 未读取到任务数据")
    except Exception as e:
        print(f"  ❌ Excel读取失败: {e}")
    
    # 测试比特浏览器连接
    print(f"\n🌐 测试比特浏览器连接:")
    try:
        from bit_api import BitBrowser
        bit_browser = BitBrowser("YouTube")
        browsers = bit_browser.browser_list()
        if browsers:
            print(f"  ✅ 找到 {len(browsers)} 个浏览器")
            for browser in browsers:
                print(f"    - {browser.get('name', 'Unknown')}")
        else:
            print(f"  ⚠️ 未找到浏览器")
    except Exception as e:
        print(f"  ❌ 比特浏览器连接失败: {e}")
    
    print(f"\n🎉 系统测试完成!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print(f"\n" + "="*50)
print(f"如果上面的测试都通过，您可以运行:")
print(f"  python run_upload.py")
print(f"="*50)
