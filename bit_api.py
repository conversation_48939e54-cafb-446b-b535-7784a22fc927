import os
import time
import random
from typing import List, <PERSON><PERSON>
import requests
import json
from faker import Faker
import time
import asyncio
from playwright.async_api import async_playwright, Playwright
import sqlite3
import aiosqlite
import logging

faker = Faker()
webgl_vendors = ['Google Inc.', 'Microsoft',
                 'Apple Inc.', 'ARM', 'Intel Inc.', 'Qualcomm']
webgl_renders = ['ANGLE (Intel(R) HD Graphics 520 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (Intel(R) HD Graphics 5300 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (Intel(R) HD Graphics 620 Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (Intel(R) HD Graphics Direct3D11 vs_4_1 ps_4_1)', 'ANGLE (NVIDIA GeForce GTX 1050 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 1050 Ti Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 1660 Ti Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce RTX 2070 SUPER Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (Intel(R) HD Graphics Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (Intel(R) HD Graphics Family Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (Intel(R) HD Graphics 4400 Direct3D11 vs_5_0 ps_5_0)', 'Intel(R) HD Graphics 4600', 'ANGLE (NVIDIA GeForce GTX 750 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA Quadro K600 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA Quadro M1000M Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (AMD Radeon (TM) R9 370 Series Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (AMD Radeon HD 7700 Series Direct3D9Ex vs_3_0 ps_3_0)', 'Apple GPU', 'Intel(R) UHD Graphics 620', 'Mali-G72', 'Mali-G72 MP3', 'ANGLE (NVIDIA GeForce GTX 750  Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 760 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 750 Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 750 Ti Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 750 Ti Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 760 Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 770 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 780 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 850M Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 850M Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 860M Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 950 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 950 Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 950M Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 950M Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 960 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 960 Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 960M Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 960M Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 970 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 970 Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 980 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 980 Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce GTX 980 Ti Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce GTX 980M Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce MX130 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce MX150 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce MX230 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce MX250 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce RTX 2060 Direct3D9Ex vs_3_0 ps_3_0)', 'ANGLE (NVIDIA GeForce RTX 2060 SUPER Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA GeForce RTX 2070 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA Quadro K620 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA Quadro FX 380 Direct3D11 vs_4_0 ps_4_0)', 'ANGLE (NVIDIA Quadro NVS 295 Direct3D11 vs_4_0 ps_4_0)', 'ANGLE (NVIDIA Quadro P1000 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA Quadro P2000 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA Quadro P400 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA Quadro P4000 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA Quadro P600 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (NVIDIA Quadro P620 Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (ATI Mobility Radeon HD 4330 Direct3D11 vs_4_1 ps_4_1)', 'ANGLE (ATI Mobility Radeon HD 4500 Series Direct3D11 vs_4_1 ps_4_1)', 'ANGLE (ATI Mobility Radeon HD 5000 Series Direct3D11 vs_5_0 ps_5_0)',
                 'ANGLE (ATI Mobility Radeon HD 5400 Series Direct3D11 vs_5_0 ps_5_0)', 'ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8935)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.6079)', 'ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-26.20.100.7870)', 'ANGLE (AMD, Radeon (TM) RX 470 Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.1034.6)', 'ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8681)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 750 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-10.18.13.6881)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 970 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.5671)', 'ANGLE (AMD, AMD Radeon(TM) Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.14028.11002)', 'ANGLE (Intel, Intel(R) HD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8681)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 750 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.5671)', 'ANGLE (AMD, AMD Radeon RX 5700 XT Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.13025.1000)', 'ANGLE (AMD, AMD Radeon RX 6900 XT Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.13011.1004)', 'ANGLE (AMD, AMD Radeon(TM) Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.13002.23)', 'ANGLE (Intel, Intel(R) HD Graphics 530 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.9466)', 'ANGLE (Intel, Intel(R) HD Graphics 5500 Direct3D11 vs_5_0 ps_5_0, D3D11-20.19.15.5126)', 'ANGLE (Intel, Intel(R) HD Graphics 6000 Direct3D11 vs_5_0 ps_5_0, D3D11-20.19.15.5126)', 'ANGLE (Intel, Intel(R) HD Graphics 610 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.9466)', 'ANGLE (Intel, Intel(R) HD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.9168)', 'ANGLE (Intel, Intel(R) HD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.6589)', 'ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.9126)', 'ANGLE (Intel, Mesa Intel(R) UHD Graphics 620 (KBL GT2), OpenGL 4.6 (Core Profile) Mesa 21.2.2)', 'ANGLE (NVIDIA Corporation, GeForce GTX 1050 Ti/PCIe/SSE2, OpenGL 4.5.0 NVIDIA 460.73.01)', 'ANGLE (NVIDIA Corporation, GeForce GTX 1050 Ti/PCIe/SSE2, OpenGL 4.5.0 NVIDIA 460.80)', 'ANGLE (NVIDIA Corporation, GeForce GTX 1050/PCIe/SSE2, OpenGL 4.5 core)', 'ANGLE (NVIDIA Corporation, GeForce GTX 1060 6GB/PCIe/SSE2, OpenGL 4.5 core)', 'ANGLE (NVIDIA Corporation, GeForce GTX 1080 Ti/PCIe/SSE2, OpenGL 4.5 core)', 'ANGLE (NVIDIA Corporation, GeForce GTX 1650/PCIe/SSE2, OpenGL 4.5 core)', 'ANGLE (NVIDIA Corporation, GeForce GTX 650/PCIe/SSE2, OpenGL 4.5 core)', 'ANGLE (NVIDIA Corporation, GeForce GTX 750 Ti/PCIe/SSE2, OpenGL 4.5 core)', 'ANGLE (NVIDIA Corporation, GeForce GTX 860M/PCIe/SSE2, OpenGL 4.5 core)', 'ANGLE (NVIDIA Corporation, GeForce GTX 950M/PCIe/SSE2, OpenGL 4.5 core)', 'ANGLE (NVIDIA Corporation, GeForce MX150/PCIe/SSE2, OpenGL 4.5 core)', 'ANGLE (NVIDIA Corporation, GeForce RTX 2070/PCIe/SSE2, OpenGL 4.5 core)', 'ANGLE (NVIDIA Corporation, NVIDIA GeForce GTX 660/PCIe/SSE2, OpenGL 4.5.0 NVIDIA 470.57.02)', 'ANGLE (NVIDIA Corporation, NVIDIA GeForce RTX 2060 SUPER/PCIe/SSE2, OpenGL 4.5.0 NVIDIA 470.63.01)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1050 Ti Direct3D9Ex vs_3_0 ps_3_0, nvd3dumx.dll-26.21.14.4250)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 5GB Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7168)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7212)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1070 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.6677)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1080 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7111)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1650 Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7212)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1650 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7111)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1660 SUPER Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7196)', 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1660 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7196)']
color_depths = [1, 2, 3, 4, 5, 8, 12, 15, 16, 18, 24, 30, 32, 48]
systems = ['Win32', 'Linux i686', 'Linux armv7l', 'MacIntel']
payload_config = {
    "groupId": "",  # 群组ID，绑定群组时传入，如果登录的是子账号，则必须赋值，否则会自动分配到主账户下面去
    "platform": '',  # 账号平台
    "platformIcon": 'other',  # 取账号平台的 hostname 或者设置为other
    "url": '',  # 打开的url，多个用,分开
    "name": '',  # 窗口名称
    # 备注
    "remark": '',
    "userName": '',  # 用户账号
    # "password": password,  # 用户密码
    "password": '',  # 用户密码
    "cookie": '',  # cookie
    "proxyMethod": 2,  # 代理类型 2自定义;3提取IP
    # 自定义代理类型 ['noproxy', 'http', 'https', 'socks5']
    "proxyType": 'noproxy',
    "host": '',  # 代理主机
    "port": '',  # 代理端口
    "proxyUserName": '',  # 代理账号
    "proxyPassword": '',  # 代理密码
    'dynamicIpUrl': '',  # proxyMethod = 3时，提取IP链接
    'dynamicIpChannel': '',  # 提取链接服务商，rola | doveip | cloudam | common
    'isDynamicIpChangeIp': False,  # 每次打开都提取新IP，默认false
    # ip检测服务IP库，默认ip-api，选项 ip-api | ip123in | luminati，luminati为Luminati专用
    'ipCheckService': 'ip-api',
    'abortImage': False,  # 是否禁止图片加载
    'abortMedia': False,  # 是否禁止媒体加载
    'stopWhileNetError': False,  # 网络错误时是否停止
    'syncTabs': False,  # 是否同步标签页
    'syncCookies': True,  # 是否同步cookie
    'syncIndexedDb': False,  # 是否同步indexedDB
    'syncBookmarks': True,  # 是否同步书签
    'syncAuthorization': False,  # 是否同步授权
    'syncHistory': True,  # 是否同步历史记录
    'isValidUsername': False,  # 是否验证用户名
    'workbench': 'localserver',
    'allowedSignin': True,  # 允许google账号登录浏览器，默认true
    'syncSessions': False,  # 同步浏览器Sessions，历史记录最近关闭的标签相关，默认false
    'clearCacheFilesBeforeLaunch': False,  # 启动前清理缓存文件，默认false
    'clearCookiesBeforeLaunch': False,  # 启动前清理cookie，默认false
    'clearHistoriesBeforeLaunch': False,  # 启动前清理历史记录，默认false
    'randomFingerprint': False,  # 是否启用随机指纹，默认false
    'disableGpu': False,  # 是否禁用GPU，默认false
    'enableBackgroundMode': False,  # 是否启用后台模式，默认false
    'muteAudio': True,  # 是否静音，默认True
}
fingerprint_config = {
    'coreVersion': '104',
    'ostype': 'PC',  # 操作系统平台 PC|Android|IOS
    'os': 'Win32',  # 为navigator.platform值 Win32 | Linux i686 | Linux armv7l | MacIntel，当ostype设置为IOS时，设置os为iPhone，ostype为Android时，设置为 Linux i686 || Linux armv7l
    'version': '',  # 浏览器版本
    'userAgent': '',
    'timeZone': '',  # 时区
    'timeZoneOffset': 0,  # 时区偏移量
    'isIpCreateTimeZone': True,  # 时区
    'webRTC': '0',  # webrtc 0|1|2
    'position': '1',  # 地理位置 0|1|2
    'isIpCreatePosition': True,  # 位置开关
    'lat': '',  # 经度
    'lng': '',  # 纬度
    'precisionData': '',  # 精度米
    'isIpCreateLanguage': False,  # 语言开关
    'languages': 'en-US',  # 默认系统
    'isIpCreateDisplayLanguage': False,  # 显示语言默认不跟随IP
    'displayLanguages': 'en-US',  # 默认系统
    'resolutionType': '0',  # 分辨
    'resolution': '',
    'fontType': '0',  # 字体生成类型
    'font': '',  # 字体
    'canvas': '0',  # canvas
    'canvasValue': None,  # canvas 噪音值 10000 - 1000000
    'webGL': '0',  # webGL
    'webGLValue': None,  # webGL 噪音值 10000 - 1000000
    'webGLMeta': '0',  # 元数据
    'webGLManufacturer': '',  # 厂商
    'webGLRender': '',  # 渲染
    'audioContext': '0',  # audioContext
    'audioContextValue': None,  # audioContext噪音值 1 - 100 ，关闭时默认10
    'mediaDevice': '0',  # mediaDevice
    'mediaDeviceValue': None,  # mediaDevice 噪音值，修改时再传回到服务端
    'speechVoices': '0',  # Speech Voices，默认随机
    'speechVoicesValue': None,  # peech Voices 值，修改时再传回到服务端
    'hardwareConcurrency': '4',  # 并发数
    'deviceMemory': '8',  # 设备内存
    'doNotTrack': '1',  # doNotTrack
    'portScanProtect': '',  # port
    'portWhiteList': '',
    'colorDepth': '32',
    'devicePixelRatio': '1.2',
    'openWidth': 1280,
    'openHeight': 1000,
    'ignoreHttpsErrors': True,  # 忽略https证书错误
    'clientRectNoiseEnabled': False,  # 默认关闭
    'clientRectNoiseValue': 0,  # 关闭为0，开启时随机 1 - 999999
    'deviceInfoEnabled': False,  # 设备信息，默认关闭
    'computerName': '',  # deviceInfoEnabled 为true时，设置
    'macAddr': ''  # deviceInfoEnabled 为true时，设置
}


class BitBrowser:

    def __init__(self, website):
        self.logger = logging.getLogger(__name__)
        self.website = website  # 网站，用于浏览器窗口名称
        self.bit_port = self.get_bit_port()

    @classmethod
    def test_port(cls, port):
        url = f'http://127.0.0.1:{port}'
        try:
            res = requests.get(url, timeout=30)
            if res.status_code == 200:
                return True
        except:
            import traceback
            traceback.print_exc()
            return False

    @classmethod
    def get_bit_port(cls):
        return 54345
        # 获取比特浏览器的本地端口
        # json_file = fr'C:\Users\<USER>\AppData\Roaming\bitbrowser\config.json'
        # if not os.path.exists(json_file):
        #     # 尝试枚举
        #     users = os.listdir('C:/Users')
        #     for user in users:
        #         x = fr'C:\Users\<USER>\AppData\Roaming\bitbrowser\config.json'
        #         if os.path.exists(x):
        #             json_file = x
        #             break
        #     else:
        #         logging.error(f'请先安装比特浏览器:{json_file}')
        #         return False
        # with open(json_file, 'r', encoding='utf-8') as f:
        #     data = json.load(f)
        #     bit_api = data['localServerAddress']
        #     cls.bit_port = bit_api.split(':')[-1]
        #     if cls.test_port(cls.bit_port):
        #         return cls.bit_port
        #     else:
        #         logging.error(f'请检查比特浏览器是否已经启动')
        #         return False

    import traceback

    def __request(self, endpoint, payload):
        """
        请求接口
        """
        endpoint = endpoint[1:] if endpoint.startswith('/') else endpoint
        api = f'http://127.0.0.1:{self.bit_port}/{endpoint}'
        self.logger.info(f"开始请求接口: {api}")
        res = None
        for _ in range(3):
            try:
                res = requests.post(api, json=payload, timeout=60)

                data = res.json()
                if data.get('success'):
                    return data
                else:
                    self.logger.error(f'endpoint: {endpoint} 请求结果: {data}')
            except requests.Timeout:
                self.logger.error(f"请求超时: {api}")
            except requests.ConnectionError:
                self.logger.error(f"连接错误: {api}")
            except json.JSONDecodeError:
                self.logger.error(f"JSON解析错误，响应内容: {res.text[:200]}...")
            except Exception as e:
                self.logger.error(f"未知错误: {str(e)}")

            self.logger.info(f"等待3秒后重试")
            time.sleep(3)
        
        self.logger.error(f"请求失败，已重试3次: {api}")
        return {}

    ##############################################浏览器接口#########################################################
    def create_driver(self, username='', password='', proxyType='noproxy', proxyIp='', proxyPort='', proxyUser='', proxyPassword='', group_id='',**kwargs):
        """
        创建浏览器
        仅传入用户名、密码、代理
        """
        # 查询是否已经创建过，如果已经创建过则保留原来的指纹
        driver = self.browser_list(username=username)
        if driver:
            self.logger.info(f'使用现有浏览器ID：{driver[0]["id"]}')
            return driver[0]['id']
        # 浏览器对象
        remark = f'{username}----{password}'
        payload = payload_config.copy()
        payload['name'] = f'{self.website}:{username}'
        payload['remark'] = remark
        payload['proxyType'] = proxyType
        payload['host'] = proxyIp
        payload['port'] = proxyPort
        payload['proxyUserName'] = proxyUser
        payload['proxyPassword'] = proxyPassword
        payload['groupId'] = group_id
        if proxyType not in ['noproxy', 'http', 'https', 'socks5']:
            if 'rola' in proxyIp:
                ProxyType = 'rola'
            elif 'doveip' in proxyIp:
                ProxyType = 'doveip'
            elif 'cloudam' in proxyIp:
                ProxyType = 'cloudam'
            else:
                ProxyType = 'common'
            # 自定义提取代理
            payload['proxyType'] = 'socks5'
            payload['host'] = ''
            payload['port'] = ''
            payload['proxyMethod'] = 3
            payload['dynamicIpChannel'] = ProxyType
            payload['dynamicIpUrl'] = proxyIp

        # 指纹对象随机生成
        fingerprint = fingerprint_config.copy()
        fingerprint['version'] = str(random.randint(98, 106))
        fingerprint['computerName'] = f'Computer-{faker.first_name()}'
        fingerprint['macAddr'] = (
            '-'.join(['%02x' % faker.pyint(0, 255) for i in range(6)])).upper()
        # fingerprint['os'] = random.choice(
        #     ['Win32', 'Linux i686', 'Linux armv7l', 'MacIntel'])
        fingerprint['webGLManufacturer'] = random.choice(webgl_vendors)
        fingerprint['webGLRender'] = random.choice(webgl_renders)
        fingerprint['colorDepth'] = random.choice(color_depths)
        fingerprint['hardwareConcurrency'] = random.choice([2, 4, 6, 8])
        fingerprint['deviceMemory'] = random.choice([4, 8, 16, 32, 64])
        fingerprint['version'] = random.randint(100, 107)
        fingerprint['canvasValue'] = random.randint(10000, 1000000)
        fingerprint['webGLValue'] = random.randint(10000, 1000000)
        fingerprint['clientRectNoiseValue'] = random.randint(1, 999999)
        fingerprint['audioContextValue'] = random.randint(1, 100)
        fingerprint['mediaDeviceValue'] = random.randint(1, 100)
        fingerprint['speechVoicesValue'] = random.randint(1, 100)
        fingerprint['resolution'] = random.choice(
            ['1024 x 768', '1280 x 800', '1280 x 960', '1920 x 1080', '1440 x 900', '1280 x 1024'])
        fingerprint['openWidth'] = fingerprint['resolution'].split(' x ')[0]
        fingerprint['openHeight'] = fingerprint['resolution'].split(' x ')[1]

        # 从kwargs更新参数
        for k, v in kwargs.items():
            if fingerprint.get(k):
                fingerprint[k] = v
            if payload.get(k):
                payload[k] = v
        payload['browserFingerPrint'] = fingerprint

        
    
        try:
            if driver:
                self.logger.info(f'使用现有浏览器ID：{driver[0]["id"]}')
                payload['id'] = driver[0]['id']
            
                # 保留原来的分组
                if driver[0].get('groupId'):
                    self.logger.info(f'使用现有分组ID：{driver[0]["groupId"]}')
                    payload['groupId'] = driver[0]['groupId']

                # 保留原来的指纹
                self.logger.info(f'开始创建浏览器指纹信息，{username}')
                browser_detail = self.browser_detail(driver[0]['id'])
                self.logger.info(f'创建浏览器指纹信息，{username}')
                payload['browserFingerPrint'] = browser_detail['browserFingerPrint']
            data = self.__request('browser/update', payload)
            if data.get('success'):
                logging.info(f'创建浏览器成功，{username}')
                return data['data']['id']
            else:
                logging.error(f'创建浏览器失败，{username}')
                return False
        except Exception as e:
            self.logger.error(f'创建浏览器失败，{username}')
            self.logger.error(f'创建浏览器失败，{e}')
            return False

    def open_browser(self, browser_id, loadExtensions=True, args=[], extractIp=True):
        """
        打开窗口
        """
        self.logger.info(f'打开窗口: {browser_id}')
        payload = {
            'id': browser_id,
            'loadExtensions': loadExtensions,
            'args': args,
            'extractIp': extractIp
        }
        data = self.__request('browser/open', payload)
        if data.get('success'):
            ws = data['data']['ws']
            print("ws address ==>>> ", ws)
            return ws
        else:
            self.logger.error(f'打开窗口失败: {browser_id}')
            return False

    def close_browser(self, browser_id):
        self.logger.info(f'关闭窗口{browser_id}')
        payload = {'id': browser_id}
        data = self.__request('browser/close', payload)
        if data.get('success'):
            self.logger.info(f'关闭窗口成功: {browser_id}')
            return True
        else:
            self.logger.error(f'关闭窗口失败: {browser_id}')
            return False


    def browser_detail(self, browser_id):
        """
        获取窗口详情
        """
        self.logger.info(f'获取窗口详情{browser_id}')
        payload = {'id': browser_id}
        try:
            data = self.__request('browser/detail', payload)
            
            if data is None:
                self.logger.error(f'获取窗口详情返回None: {browser_id}')
                return False
            if data.get('success'):
                self.logger.info(f'获取窗口详情成功: {browser_id}')
                return data.get('data')
            else:
                self.logger.error(f'获取窗口详情失败: {browser_id}, 错误信息: {data.get("message")}')
                return False
        except Exception as e:
            self.logger.exception(f'获取窗口详情时发生异常: {browser_id}, 异常信息: {str(e)}')
            return False

    def browser_list(self, page=0, pageSize=100, username: str = '', groupId=''):
        """
        查询窗口
        """
        self.logger.info(f'查询窗口: {username}')
        payload = {
            "page": page,
            "pageSize": pageSize,
        }
        if username != '':
            payload['name'] = f'{self.website}:{username}'
        if groupId != '':
            payload['groupId'] = groupId
        return self.__request('browser/list', payload).get('data', {'list': []}).get('list', [])

    def windowbounds(self, type: str = 'box', startX: int = 0, startY: int = 0, width: int = 500, height: int = 500, col: int = 3, spaceX: int = 0, spaceY: int = 0, offsetX: int = 0, offsetY: int = 0,ids:list=[]):
        if type not in ['box', 'diagonal']:
            self.logger.error('排列方式错误,自动选用box')
            type = 'box'
        if width < 500:
            self.logger.error('宽度最小500,自动设置为500')
            width = 500
        if height < 200:
            self.logger.error('高度最小200,自动设置为200')
            height = 200
        payload = {
            'type': type,
            'startX': startX,
            'startY': startY,
            'width': width,
            'height': height,
            'col': col,
            'spaceX': spaceX,
            'spaceY': spaceY,
            'offsetX': offsetX,
            'offsetY': offsetY,
            'ids': ids
        }
        data = self.__request('windowbounds', payload)
        if data.get('success'):
            self.logger.info(f'窗口排列成功')
            return True
        else:
            self.logger.error(f'窗口排列失败')
            return False

    def update_browser_group(self, group_id, browser_ids):
        """
        更新窗口分组
        """
        self.logger.info(f'更新窗口分组{group_id}')
        payload = {
            'groupId': group_id,
            'browserIds': browser_ids
        }
        data = self.__request('browser/group/update', payload)
        if data.get('success'):
            self.logger.info(f'更新窗口分组成功: {group_id}')
            return True
        else:
            self.logger.error(f'更新窗口分组失败: {group_id}')
            return False


    def batch_close_browser_by_seq(self, seqs: list):
        """
        批量通过序号关闭窗口
        """
        self.logger.info(f'批量通过序号关闭窗口')
        payload = {
            'seqs': seqs
        }
        data = self.__request('browser/close/byseqs', payload)
        if data.get('success'):
            self.logger.info(f'批量通过序号关闭窗口成功')
            return True
        else:
            self.logger.error(f'批量通过序号关闭窗口失败')
            return False

    ###################################################分组接口########################################################
    def add_group(self, group_name):
        """
        添加分组
        """
        self.logger.info(f'添加分组{group_name}')
        payload = {'groupName': group_name, 'sortNum': 1}
        data = self.__request('group/add', payload)
        if data.get('success'):
            self.logger.info(f'添加分组成功: {group_name}')
            return data['data']['id']
        else:
            self.logger.error(f'添加分组失败: {group_name}')
            return False

    def group_list(self, page=0, pageSize=100):
        """
        查询分组列表，传入group_name则查询指定的分组
        """
        self.logger.info(f'查询分组列表')
        payload = {
            'page': page,
            'pageSize': pageSize
        }
        data = self.__request('group/list', payload)
        if data.get('success'):
            self.logger.info(f'查询分组列表成功')
            return data['data']['list']
        else:
            self.logger.error(f'查询分组列表失败')
            return []

    def group_detail(self, group_id):
        """
        查询分组详情
        """
        self.logger.info(f'查询分组详情')
        payload = {'id': group_id}
        data = self.__request('group/detail', payload)
        if data.get('success'):
            self.logger.info(f'查询分组详情成功')
            return data
        else:
            self.logger.error(f'查询分组详情失败')
            return False
        
    def query_group_id(self, group_name):
        """
        查询指定分组存在不存在，如果存在，返回分组id
        """
        self.logger.info(f'查询指定分组:{group_name}')
        for _ in range(5):
            group_list = self.group_list(_)
            for group in group_list:
                if group['groupName'] == group_name:
                    group_id = group['id']
                    self.logger.info(f'查询指定分组成功:{group_name}')
                    return group_id
        else:
            self.logger.error(f'查询指定分组失败:{group_name}')
            return False
    def get_browser_id(self, username='', password='', proxyType='noproxy', proxyIp='', proxyPort='', proxyUsername='', proxyPassword='', group_id='', **kwargs):
        """
        便捷创建并打开浏览器，返回driver对象和浏览器id
        """
        browser_id = self.create_driver(
            username, password, proxyType, proxyIp, proxyPort, proxyUsername, proxyPassword, group_id, **kwargs)
        return browser_id

    async def get_users_for_shard(self, db_path: str, shard_index: int, total_shards: int, limit: int = 10) -> List[Tuple[int, str]]:
        try:
            async with aiosqlite.connect(db_path) as db:
                query = """
                SELECT id, comment_nickname 
                FROM note_comment 
                WHERE at_status = 0 AND id % ? = ?
                LIMIT ?
                """
                async with db.execute(query, (total_shards, shard_index, limit)) as cursor:
                    users = await cursor.fetchall()
                self.logger.info(f'获取分片 {shard_index + 1}/{total_shards} 的用户信息，数量：{len(users)}')
                return users
        except Exception as e:
            self.logger.error(f'获取分片 {shard_index + 1}/{total_shards} 的用户信息时发生错误: {str(e)}')
            return []

    async def update_user_status(self, db_path: str, user_ids: List[int]) -> None:
        try:
            async with aiosqlite.connect(db_path) as db:
                placeholders = ','.join(['?' for _ in user_ids])
                query = f"UPDATE note_comment SET at_status = 1 WHERE id IN ({placeholders})"
                await db.execute(query, user_ids)
                await db.commit()
                self.logger.info(f'更新了 {len(user_ids)} 个用户的状态')
        except Exception as e:
            self.logger.error(f'更新用户状态时发生错误: {str(e)}')

    async def process_browser(self, browser_ws: str, db_path: str, shard_index: int, total_shards: int, note_link: str) -> None:
        self.logger.info(f'开始处理浏览器{browser_ws},分片{shard_index},总片数：{total_shards}')
        async with async_playwright() as p:
            browser = await p.chromium.connect_over_cdp(browser_ws)
            default_context = browser.contexts[0]
            page = await default_context.new_page()
            await page.goto(note_link, timeout=30000)
        
            try:
                while True:
                    try:
                        users = await self.get_users_for_shard(db_path, shard_index, total_shards)
                        if not users:
                            self.logger.info(f'分片 {shard_index + 1}/{total_shards} 的所有用户已处理完毕')
                            break
                        
                        try:
                            await page.goto(note_link, timeout=30000)
                            await page.wait_for_selector('span:text-matches("说点什么.*")', timeout=10000)
                            await page.click('span:text-matches("说点什么.*")')
                            await page.wait_for_selector('#content-textarea', state='visible', timeout=5000)
                        except Exception as e:
                            self.logger.error(f"页面加载或找到评论框失败: {str(e)}")
                            continue

                        for user in users:
                            try:
                                await self.process_user(page, user)
                            except Exception as e:
                                self.logger.error(f"处理用户 {user[1]} 时出错: {str(e)}")

                        try:
                            submit_button = await page.query_selector('button.btn.submit:has-text("发送")')
                            if submit_button:
                                self.logger.info(f'提交评论')
                                await submit_button.click()
                            else:
                                self.logger.warning('未找到发送按钮')
                        except Exception as e:
                            self.logger.error(f"提交评论时出错: {str(e)}")

                        await asyncio.sleep(random.uniform(2, 4))

                        try:
                            # 更新处理过的用户状态
                            user_ids = [user[0] for user in users]
                            await self.update_user_status(db_path, user_ids)
                        except Exception as e:
                            self.logger.error(f"更新用户状态时出错: {str(e)}")

                        await asyncio.sleep(random.uniform(5, 10))
                    except Exception as e:
                        self.logger.error(f"处理分片 {shard_index + 1}/{total_shards} 时发生未预期的错误: {str(e)}")
                        await asyncio.sleep(30)  # 发生错误时等待30秒后继续
            except Exception as e:
                self.logger.error(f"处理浏览器 {browser_ws} 时发生致命错误: {str(e)}")

    async def process_user(self, page, user: Tuple[int, str]) -> None:
        try:
            comment = f"@{user[1]}"
            await page.type('#content-textarea', comment, delay=500)

            try:
                mention_list = await page.wait_for_selector('#mentionList', timeout=5000)
                if mention_list:
                    first_option = await mention_list.query_selector('li:first-child')
                    if first_option:
                        self.logger.info(f'点击用户:{user[1]}')
                        await first_option.click()
                        await page.wait_for_timeout(1000)
                    else:
                        self.logger.warning(f'未找到用户选项:{user[1]}')
                else:
                    self.logger.warning(f'未出现mentionList:{user[1]}')
            except Exception as e:
                self.logger.error(f'处理mentionList时出错: {str(e)}')

        except Exception as e:
            self.logger.error(f'处理用户 {user[1]} 时出错: {str(e)}')

    async def init_browser(self,windows_name: str, group_name: str,browser_count: int):
        # bit = BitBrowser(windows_name)
        self.website = windows_name
        group_id = self.query_group_id(group_name)
        if not group_id:
            group_id = self.add_group(group_name)

        browser_ids = []
        ws_list = []

        # 创建5个浏览器
        for i in range(1,browser_count+ 1):
            await asyncio.sleep(1)
            browser_id = self.get_browser_id(username=f'{i}', password='', proxyType='noproxy',
                                            proxyIp='', proxyPort='', proxyUsername='', proxyPassword='', 
                                            dynamicIpUrl='', group_id=group_id)
            browser_ids.append(browser_id)
                
            ws = self.open_browser(browser_id)
            if ws:
                ws_list.append(ws)
            else:
                self.logger.error(f'打开浏览器失败: {browser_id}')

        self.windowbounds(type='box', startX=0, startY=0, width=350, height=500, col=5, spaceX=0, spaceY=0, offsetX=0, offsetY=0,ids=browser_ids)
        return browser_ids,ws_list