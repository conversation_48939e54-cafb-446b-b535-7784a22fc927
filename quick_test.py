#!/usr/bin/env python3
"""
快速测试脚本
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    print("🚀 快速测试开始...")
    
    try:
        print("1. 测试Excel读取...")
        from youtube_uploader.excel_handler import ExcelHandler
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        print(f"✅ 读取到 {len(tasks)} 个任务")
        
        print("2. 测试TaskExecutor导入...")
        from youtube_uploader.executor import TaskExecutor
        print("✅ TaskExecutor导入成功")
        
        print("3. 测试TaskExecutor创建...")
        config = {'max_retries': 3}
        executor = TaskExecutor(config)
        print("✅ TaskExecutor创建成功")
        
        print("4. 测试profile检测...")
        task = tasks[0]
        exists = executor.bit_browser_manager.check_profile_exists(task.profile_name)
        print(f"✅ Profile '{task.profile_name}' 存在: {exists}")
        
        print("🎉 所有测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
