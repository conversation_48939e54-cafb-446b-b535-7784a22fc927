#!/usr/bin/env python3
"""
模拟实际执行环境的测试脚本
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('execution_test.log', encoding='utf-8')
        ]
    )

async def test_actual_execution():
    """测试实际执行环境"""
    print("🔍 测试实际执行环境")
    print("=" * 60)
    
    try:
        # 1. 测试Excel读取
        print("\n1️⃣ 测试Excel读取...")
        from youtube_uploader.excel_handler import ExcelHandler
        
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        
        if not tasks:
            print("❌ 未找到任务数据")
            return False
        
        print(f"✅ 读取到 {len(tasks)} 个任务")
        for i, task in enumerate(tasks):
            print(f"   任务 {i+1}: {task.title} ({task.profile_name})")
        
        # 2. 测试TaskExecutor初始化
        print(f"\n2️⃣ 测试TaskExecutor初始化...")
        from youtube_uploader.executor import TaskExecutor
        
        config = {
            'max_retries': 3,
            'retry_delay': 5,
            'page_load_timeout': 30,
            'element_timeout': 15,
            'upload_timeout': 600,
            'screenshot_dir': 'screenshots'
        }
        
        executor = TaskExecutor(config)
        print("✅ TaskExecutor初始化成功")
        
        # 3. 测试BitBrowserManager
        print(f"\n3️⃣ 测试BitBrowserManager...")
        bit_manager = executor.bit_browser_manager
        
        print(f"BitBrowserManager website: {bit_manager.website}")
        print(f"BitBrowserManager max_retries: {bit_manager.max_retries}")
        
        # 4. 测试profile检测
        print(f"\n4️⃣ 测试profile检测...")
        for task in tasks:
            profile_name = task.profile_name
            print(f"\n检测profile: '{profile_name}'")
            
            try:
                # 检查比特浏览器是否运行
                if not bit_manager.is_bit_browser_running():
                    print("❌ 比特浏览器未运行")
                    return False
                else:
                    print("✅ 比特浏览器运行正常")
                
                # 检查profile是否存在
                exists = bit_manager.check_profile_exists(profile_name)
                print(f"Profile '{profile_name}' 存在: {exists}")
                
                if exists:
                    # 尝试获取浏览器ID
                    try:
                        browser_id = bit_manager.get_browser_id(profile_name)
                        print(f"✅ 浏览器ID: {browser_id}")
                    except Exception as e:
                        print(f"❌ 获取浏览器ID失败: {e}")
                        
                else:
                    print(f"❌ Profile '{profile_name}' 不存在")
                    
                    # 调试：显示所有可用的profiles
                    print("🔍 调试：显示所有可用的profiles")
                    try:
                        all_browsers = bit_manager.bit_browser.browser_list()
                        print(f"找到 {len(all_browsers)} 个浏览器:")
                        for browser in all_browsers:
                            print(f"  - ID: {browser.get('id', 'N/A')}, Name: {browser.get('name', 'N/A')}")
                    except Exception as e:
                        print(f"获取浏览器列表失败: {e}")
                        
            except Exception as e:
                print(f"❌ 检测profile时出错: {e}")
                import traceback
                traceback.print_exc()
        
        # 5. 测试实际的任务执行流程
        print(f"\n5️⃣ 测试任务执行流程...")
        
        # 模拟_execute_profile_tasks的开始部分
        for task in tasks:
            profile_name = task.profile_name
            print(f"\n模拟执行profile '{profile_name}' 的任务...")
            
            # 检查 profile 是否存在
            if not bit_manager.check_profile_exists(profile_name):
                print(f"❌ Profile '{profile_name}' 不存在，跳过相关任务")
                continue
            else:
                print(f"✅ Profile '{profile_name}' 存在")
            
            # 尝试打开浏览器（但不实际执行）
            print(f"🔍 模拟打开浏览器...")
            try:
                # 这里我们不实际打开浏览器，只是测试到这一步
                print(f"✅ 模拟成功")
            except Exception as e:
                print(f"❌ 模拟失败: {e}")
        
        print(f"\n✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    setup_logging()
    
    print("🚀 实际执行环境测试工具")
    
    # 检查必要文件
    if not Path('tasks_template.xlsx').exists():
        print("❌ 未找到 tasks_template.xlsx 文件")
        return
    
    # 运行测试
    result = asyncio.run(test_actual_execution())
    
    print("\n" + "=" * 60)
    if result:
        print("🎉 测试成功！")
    else:
        print("❌ 测试失败！")
    print("=" * 60)

if __name__ == "__main__":
    main()
