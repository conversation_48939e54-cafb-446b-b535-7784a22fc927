#!/usr/bin/env python3
"""
快速诊断工具
一键分析当前页面问题
"""

import sys
import asyncio
from pathlib import Path
from playwright.async_api import async_playwright

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

async def quick_diagnosis():
    """快速诊断当前页面问题"""
    print("🔍 启动快速诊断...")
    
    try:
        # 连接到已打开的比特浏览器
        from youtube_uploader.executor import TaskExecutor
        from youtube_uploader.excel_handler import ExcelHandler
        
        # 读取任务
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        task = tasks[0]
        
        # 初始化执行器
        config = {'max_retries': 3}
        executor = TaskExecutor(config)
        
        # 打开浏览器
        print(f"📱 连接到浏览器...")
        ws_address = executor.bit_browser_manager.open_browser(task.profile_name)
        
        if not ws_address:
            print("❌ 无法打开浏览器")
            return
            
        # 连接到浏览器
        async with async_playwright() as p:
            browser = await p.chromium.connect_over_cdp(ws_address)
            context = browser.contexts[0] if browser.contexts else await browser.new_context()
            page = await context.new_page()
            
            print("✅ 已连接到浏览器")
            
            # 获取当前页面信息
            title = await page.title()
            url = page.url
            
            print(f"\n📄 当前页面:")
            print(f"   标题: {title}")
            print(f"   URL: {url}")
            
            # 快速诊断
            print(f"\n🔍 快速诊断结果:")
            
            # 1. 检查是否在YouTube
            if 'youtube.com' not in url.lower():
                print("❌ 不在YouTube页面")
                return
            else:
                print("✅ 在YouTube页面")
            
            # 2. 检查页面类型
            if '/upload' in url:
                print("✅ 在上传页面")
                await diagnose_upload_page(page)
            elif '/watch' in url:
                print("✅ 在视频观看页面")
                await diagnose_watch_page(page)
            elif '/studio' in url:
                print("✅ 在YouTube Studio页面")
                await diagnose_studio_page(page)
            else:
                print("⚠️ 在其他YouTube页面")
                await diagnose_general_page(page)
            
            await page.close()
            await browser.close()
            
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()

async def diagnose_upload_page(page):
    """诊断上传页面"""
    print("\n📤 上传页面诊断:")
    
    # 检查文件上传元素
    file_input = await page.query_selector('input[type="file"]')
    if file_input:
        print("✅ 找到文件上传输入")
        is_visible = await file_input.is_visible()
        print(f"   可见性: {is_visible}")
    else:
        print("❌ 未找到文件上传输入")
    
    # 检查上传进度
    progress_bars = await page.query_selector_all('[role="progressbar"]')
    if progress_bars:
        print(f"⏳ 找到 {len(progress_bars)} 个进度条")
        for i, bar in enumerate(progress_bars):
            is_visible = await bar.is_visible()
            if is_visible:
                value = await bar.get_attribute('aria-valuenow')
                max_value = await bar.get_attribute('aria-valuemax')
                print(f"   进度条 {i+1}: {value}/{max_value}")
    else:
        print("⚪ 未找到进度条")
    
    # 检查标题输入
    title_inputs = await page.query_selector_all('div[contenteditable="true"], textarea, input[type="text"]')
    if title_inputs:
        print(f"📝 找到 {len(title_inputs)} 个输入框")
        for i, input_elem in enumerate(title_inputs[:3]):
            is_visible = await input_elem.is_visible()
            if is_visible:
                text = await input_elem.text_content()
                print(f"   输入框 {i+1}: {text[:30] if text else '(空)'}")
    
    # 检查发布按钮
    publish_buttons = await page.query_selector_all('[aria-label*="publish" i], [aria-label*="发布" i], button:has-text("发布"), button:has-text("Publish")')
    if publish_buttons:
        print(f"🚀 找到 {len(publish_buttons)} 个发布按钮")
        for i, btn in enumerate(publish_buttons):
            is_visible = await btn.is_visible()
            is_enabled = await btn.is_enabled()
            text = await btn.text_content()
            print(f"   按钮 {i+1}: 可见={is_visible}, 可用={is_enabled}, 文本='{text}'")
    else:
        print("❌ 未找到发布按钮")

async def diagnose_watch_page(page):
    """诊断观看页面"""
    print("\n📺 观看页面诊断:")
    
    # 检查视频播放器
    video_player = await page.query_selector('video')
    if video_player:
        print("✅ 找到视频播放器")
        is_playing = await video_player.evaluate('video => !video.paused')
        current_time = await video_player.evaluate('video => video.currentTime')
        duration = await video_player.evaluate('video => video.duration')
        print(f"   播放状态: {'播放中' if is_playing else '暂停'}")
        print(f"   时间: {current_time:.1f}s / {duration:.1f}s")
    else:
        print("❌ 未找到视频播放器")

async def diagnose_studio_page(page):
    """诊断Studio页面"""
    print("\n🎬 Studio页面诊断:")
    
    # 检查视频列表
    video_items = await page.query_selector_all('[aria-label*="video" i]')
    if video_items:
        print(f"📹 找到 {len(video_items)} 个视频项目")
    else:
        print("❌ 未找到视频项目")

async def diagnose_general_page(page):
    """诊断一般页面"""
    print("\n🌐 一般页面诊断:")
    
    # 检查加载状态
    loading_elements = await page.query_selector_all('[aria-label*="loading" i], .loading, .spinner')
    if loading_elements:
        print(f"⏳ 页面正在加载 ({len(loading_elements)} 个加载指示器)")
    else:
        print("✅ 页面加载完成")
    
    # 检查错误信息
    error_elements = await page.query_selector_all('[role="alert"], .error')
    if error_elements:
        print(f"🚨 发现 {len(error_elements)} 个错误信息")
        for i, error in enumerate(error_elements[:3]):
            text = await error.text_content()
            if text:
                print(f"   错误 {i+1}: {text[:50]}...")
    else:
        print("✅ 无错误信息")

if __name__ == "__main__":
    print("🚀 YouTube页面快速诊断工具")
    print("=" * 60)
    
    asyncio.run(quick_diagnosis())
