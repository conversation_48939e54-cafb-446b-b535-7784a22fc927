"""
简化的YouTube自动化模块
专注于核心功能：上传、标题、发布
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from pathlib import Path
from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError

from .models import TaskModel, ResultModel, TaskStatus, VisibilityType
from .utils import take_screenshot


class SimpleYouTubeAutomation:
    """简化的YouTube自动化类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化"""
        self.logger = logging.getLogger(__name__)
        self.config = config
    
    async def upload_video(self, page: Page, task: TaskModel) -> ResultModel:
        """上传视频到YouTube"""
        self.logger.info(f"开始上传视频: {task.title}")
        
        try:
            # 1. 导航到上传页面
            await self._navigate_to_upload_page(page)
            
            # 2. 上传文件
            await self._upload_file(page, task.video_path)
            
            # 3. 等待上传完成并填写标题
            await self._wait_and_fill_title(page, task.title)
            
            # 4. 导航到发布页面并发布
            video_url = await self._navigate_and_publish(page, task)
            
            # 5. 创建成功结果
            result = ResultModel(
                task_id=task.get_task_id(),
                profile_name=task.profile_name,
                video_path=task.video_path,
                status=TaskStatus.SUCCESS,
                video_url=video_url,
                video_id=self._extract_video_id(video_url) if video_url else None
            )
            
            self.logger.info(f"视频上传成功: {video_url}")
            return result
            
        except Exception as e:
            self.logger.error(f"视频上传失败: {e}")
            
            # 截图保存错误状态
            screenshot_path = await take_screenshot(
                page, 
                self.config.get('screenshot_dir', 'screenshots'),
                f"upload_error_{task.profile_name}"
            )
            
            # 创建失败结果
            result = ResultModel(
                task_id=task.get_task_id(),
                profile_name=task.profile_name,
                video_path=task.video_path,
                status=TaskStatus.FAILED,
                error_message=str(e),
                screenshot_path=screenshot_path
            )
            
            return result
    
    async def _navigate_to_upload_page(self, page: Page):
        """导航到上传页面"""
        self.logger.info("导航到YouTube上传页面")
        
        try:
            await page.goto('https://www.youtube.com/upload', wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(3000)
            
            if 'accounts.google.com' in page.url or 'signin' in page.url:
                raise Exception("需要登录YouTube账号")
                
            self.logger.info("✅ 成功导航到YouTube上传页面")
            
        except Exception as e:
            raise Exception(f"导航到上传页面失败: {e}")
    
    async def _upload_file(self, page: Page, video_path: str):
        """上传视频文件"""
        self.logger.info(f"上传视频文件: {video_path}")
        
        if not Path(video_path).exists():
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        try:
            # 查找并使用文件输入元素
            file_inputs = await page.query_selector_all('input[type="file"]')
            
            if not file_inputs:
                raise Exception("未找到文件输入元素")
            
            # 使用第一个文件输入元素
            await file_inputs[0].set_input_files(video_path)
            self.logger.info("✅ 文件上传成功")
            
        except Exception as e:
            raise Exception(f"文件上传失败: {e}")
    
    async def _wait_and_fill_title(self, page: Page, title: str):
        """等待上传完成并填写标题"""
        self.logger.info("等待上传完成并填写标题...")
        
        # 等待标题输入框出现（说明上传完成）
        max_wait = 300  # 5分钟
        start_time = asyncio.get_event_loop().time()
        
        while True:
            current_time = asyncio.get_event_loop().time()
            if current_time - start_time > max_wait:
                raise TimeoutError("等待标题输入框超时")
            
            # 查找标题输入框
            title_inputs = await page.query_selector_all('#textbox')
            
            for title_input in title_inputs:
                try:
                    if await title_input.is_visible():
                        # 找到可见的标题输入框
                        self.logger.info("✅ 找到标题输入框，开始填写标题")
                        
                        # 清空并填写标题
                        await title_input.click()
                        await page.keyboard.press('Control+a')
                        await page.keyboard.press('Delete')
                        await title_input.type(title, delay=50)
                        await page.wait_for_timeout(1000)
                        
                        self.logger.info(f"✅ 标题填写完成: {title}")
                        return
                except:
                    continue
            
            # 等待2秒后重试
            await page.wait_for_timeout(2000)
    
    async def _navigate_and_publish(self, page: Page, task: TaskModel) -> Optional[str]:
        """导航到发布页面并发布视频"""
        self.logger.info("导航到发布页面...")
        
        # 尝试点击下一步按钮直到到达发布页面
        max_steps = 5
        
        for step in range(max_steps):
            self.logger.info(f"尝试步骤 {step + 1}...")
            
            # 等待页面稳定
            await page.wait_for_timeout(3000)
            
            # 检查是否已经在发布页面
            if await self._is_on_publish_page(page):
                self.logger.info("✅ 已到达发布页面")
                break
            
            # 尝试点击下一步按钮
            next_clicked = await self._try_click_next(page)
            
            if not next_clicked:
                self.logger.warning(f"步骤 {step + 1}: 未找到下一步按钮")
                # 尝试按回车键
                try:
                    await page.keyboard.press('Enter')
                    await page.wait_for_timeout(2000)
                except:
                    pass
        
        # 设置可见性并发布
        await self._set_visibility_and_publish(page, task)
        
        # 等待发布完成并获取URL
        return await self._wait_for_publish_complete(page)
    
    async def _is_on_publish_page(self, page: Page) -> bool:
        """检查是否在发布页面"""
        # 查找发布页面的特征元素
        indicators = [
            'text="Publish"',
            'text="Public"',
            'text="Private"', 
            'text="Unlisted"',
            '[aria-label="Publish"]'
        ]
        
        for indicator in indicators:
            try:
                elements = await page.query_selector_all(indicator)
                for element in elements:
                    if await element.is_visible():
                        return True
            except:
                continue
        
        return False
    
    async def _try_click_next(self, page: Page) -> bool:
        """尝试点击下一步按钮"""
        next_selectors = [
            'button:has-text("Next")',
            'button:has-text("下一步")',
            '[aria-label="Next"]',
            'ytcp-button[aria-label="Next"]'
        ]
        
        for selector in next_selectors:
            try:
                buttons = await page.query_selector_all(selector)
                for button in buttons:
                    if await button.is_visible() and await button.is_enabled():
                        await button.click()
                        await page.wait_for_timeout(2000)
                        self.logger.info(f"✅ 点击了下一步按钮")
                        return True
            except:
                continue
        
        return False
    
    async def _set_visibility_and_publish(self, page: Page, task: TaskModel):
        """设置可见性并发布"""
        self.logger.info("设置可见性并发布...")
        
        # 尝试设置可见性（可选）
        try:
            if task.visibility == VisibilityType.PUBLIC:
                await page.click('text="Public"', timeout=5000)
            elif task.visibility == VisibilityType.PRIVATE:
                await page.click('text="Private"', timeout=5000)
            else:  # UNLISTED
                await page.click('text="Unlisted"', timeout=5000)
            
            await page.wait_for_timeout(1000)
            self.logger.info(f"✅ 可见性设置为: {task.visibility.value}")
        except:
            self.logger.warning("设置可见性失败，使用默认设置")
        
        # 点击发布按钮
        publish_selectors = [
            'button:has-text("Publish")',
            'button:has-text("发布")',
            '[aria-label="Publish"]',
            'ytcp-button[aria-label="Publish"]'
        ]
        
        for selector in publish_selectors:
            try:
                buttons = await page.query_selector_all(selector)
                for button in buttons:
                    if await button.is_visible() and await button.is_enabled():
                        await button.click()
                        await page.wait_for_timeout(3000)
                        self.logger.info("✅ 点击了发布按钮")
                        return
            except:
                continue
        
        raise Exception("未找到发布按钮")
    
    async def _wait_for_publish_complete(self, page: Page) -> Optional[str]:
        """等待发布完成并获取视频URL"""
        self.logger.info("等待发布完成...")
        
        # 等待发布完成（最多2分钟）
        max_wait = 120
        start_time = asyncio.get_event_loop().time()
        
        while True:
            current_time = asyncio.get_event_loop().time()
            if current_time - start_time > max_wait:
                self.logger.warning("发布等待超时，但可能已经成功")
                break
            
            # 检查是否有完成指示器
            done_indicators = [
                'text="Done"',
                'text="完成"',
                'text="Video published"',
                'text="视频已发布"'
            ]
            
            for indicator in done_indicators:
                try:
                    elements = await page.query_selector_all(indicator)
                    if elements:
                        for element in elements:
                            if await element.is_visible():
                                self.logger.info("✅ 发布完成")
                                return await self._extract_video_url(page)
                except:
                    continue
            
            # 检查URL是否包含视频ID
            if '/watch?v=' in page.url:
                self.logger.info("✅ 检测到视频URL")
                return page.url
            
            await page.wait_for_timeout(3000)
        
        # 尝试从页面获取视频URL
        return await self._extract_video_url(page)
    
    async def _extract_video_url(self, page: Page) -> Optional[str]:
        """从页面提取视频URL"""
        try:
            # 方法1: 从当前URL获取
            if '/watch?v=' in page.url:
                return page.url
            
            # 方法2: 从页面链接获取
            links = await page.query_selector_all('a[href*="/watch?v="]')
            if links:
                href = await links[0].get_attribute('href')
                if href:
                    return f"https://www.youtube.com{href}" if href.startswith('/') else href
            
            return None
            
        except Exception as e:
            self.logger.warning(f"提取视频URL失败: {e}")
            return None
    
    def _extract_video_id(self, video_url: str) -> Optional[str]:
        """从视频URL提取视频ID"""
        if not video_url:
            return None
        
        import re
        match = re.search(r'[?&]v=([^&]+)', video_url)
        return match.group(1) if match else None
