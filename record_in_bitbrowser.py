#!/usr/bin/env python3
"""
在比特浏览器中录制操作
使用浏览器开发者工具生成选择器
"""

import sys
import asyncio
from pathlib import Path
from playwright.async_api import async_playwright

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

async def record_in_bitbrowser():
    """在比特浏览器中进行录制"""
    print("🎬 在比特浏览器中录制操作...")
    print("=" * 60)
    
    try:
        # 连接到比特浏览器
        from youtube_uploader.executor import TaskExecutor
        from youtube_uploader.excel_handler import ExcelHandler
        
        # 读取任务
        excel_handler = ExcelHandler()
        tasks = excel_handler.read_tasks('tasks_template.xlsx')
        task = tasks[0]
        
        # 初始化执行器
        config = {'max_retries': 3}
        executor = TaskExecutor(config)
        
        # 打开浏览器
        print(f"📱 打开比特浏览器 profile: {task.profile_name}")
        ws_address = executor.bit_browser_manager.open_browser(task.profile_name)
        
        if not ws_address:
            print("❌ 无法打开浏览器")
            return
            
        print(f"✅ 浏览器已连接: {ws_address}")
        
        # 连接到浏览器
        async with async_playwright() as p:
            browser = await p.chromium.connect_over_cdp(ws_address)
            context = browser.contexts[0] if browser.contexts else await browser.new_context()
            page = await context.new_page()
            
            print("\n🌐 导航到YouTube上传页面...")
            await page.goto('https://www.youtube.com/upload', wait_until='networkidle')
            
            print("\n📋 手动录制指南:")
            print("=" * 40)
            print("1. 现在比特浏览器已经打开YouTube上传页面")
            print("2. 请在比特浏览器中按 F12 打开开发者工具")
            print("3. 在开发者工具中:")
            print("   - 点击 Console 标签")
            print("   - 输入以下代码来启用元素选择器录制:")
            print()
            print("// 复制粘贴这段代码到控制台:")
            print("window.recordedActions = [];")
            print("document.addEventListener('click', function(e) {")
            print("    const selector = getSelector(e.target);")
            print("    window.recordedActions.push({")
            print("        action: 'click',")
            print("        selector: selector,")
            print("        text: e.target.textContent?.trim() || '',")
            print("        timestamp: Date.now()")
            print("    });")
            print("    console.log('Recorded click:', selector);")
            print("});")
            print()
            print("function getSelector(element) {")
            print("    if (element.id) return '#' + element.id;")
            print("    if (element.className) {")
            print("        const classes = element.className.split(' ').filter(c => c);")
            print("        if (classes.length > 0) return '.' + classes.join('.');")
            print("    }")
            print("    let path = [];")
            print("    while (element.parentNode) {")
            print("        let selector = element.tagName.toLowerCase();")
            print("        if (element.id) {")
            print("            selector += '#' + element.id;")
            print("            path.unshift(selector);")
            print("            break;")
            print("        } else if (element.className) {")
            print("            const classes = element.className.split(' ').filter(c => c);")
            print("            if (classes.length > 0) selector += '.' + classes.join('.');")
            print("        }")
            print("        path.unshift(selector);")
            print("        element = element.parentNode;")
            print("    }")
            print("    return path.join(' > ');")
            print("}")
            print()
            print("4. 执行完整的视频上传流程")
            print("5. 完成后在控制台输入: console.log(JSON.stringify(window.recordedActions, null, 2))")
            print("6. 复制输出的JSON数据")
            print()
            print("按回车键继续...")
            input()
            
            # 注入录制脚本
            print("🔧 注入录制脚本...")
            await page.evaluate("""
                window.recordedActions = [];
                window.recordingEnabled = true;
                
                // 录制点击事件
                document.addEventListener('click', function(e) {
                    if (!window.recordingEnabled) return;
                    
                    const selector = getSelector(e.target);
                    const action = {
                        action: 'click',
                        selector: selector,
                        text: e.target.textContent?.trim() || '',
                        tagName: e.target.tagName,
                        type: e.target.type || '',
                        ariaLabel: e.target.getAttribute('aria-label') || '',
                        timestamp: Date.now()
                    };
                    
                    window.recordedActions.push(action);
                    console.log('🎬 Recorded click:', action);
                }, true);
                
                // 录制输入事件
                document.addEventListener('input', function(e) {
                    if (!window.recordingEnabled) return;
                    
                    const selector = getSelector(e.target);
                    const action = {
                        action: 'input',
                        selector: selector,
                        value: e.target.value || e.target.textContent || '',
                        tagName: e.target.tagName,
                        type: e.target.type || '',
                        contentEditable: e.target.contentEditable,
                        timestamp: Date.now()
                    };
                    
                    window.recordedActions.push(action);
                    console.log('🎬 Recorded input:', action);
                }, true);
                
                // 录制文件上传
                document.addEventListener('change', function(e) {
                    if (!window.recordingEnabled) return;
                    if (e.target.type === 'file') {
                        const selector = getSelector(e.target);
                        const action = {
                            action: 'file_upload',
                            selector: selector,
                            files: Array.from(e.target.files).map(f => f.name),
                            timestamp: Date.now()
                        };
                        
                        window.recordedActions.push(action);
                        console.log('🎬 Recorded file upload:', action);
                    }
                }, true);
                
                function getSelector(element) {
                    // 优先使用aria-label
                    if (element.getAttribute('aria-label')) {
                        return `[aria-label="${element.getAttribute('aria-label')}"]`;
                    }
                    
                    // 使用ID
                    if (element.id) {
                        return '#' + element.id;
                    }
                    
                    // 使用data属性
                    const dataTestId = element.getAttribute('data-testid');
                    if (dataTestId) {
                        return `[data-testid="${dataTestId}"]`;
                    }
                    
                    // 使用类名（过滤掉动态类名）
                    if (element.className && typeof element.className === 'string') {
                        const classes = element.className.split(' ')
                            .filter(c => c && !c.match(/^[a-z0-9]{6,}$/i)) // 过滤掉哈希类名
                            .slice(0, 3); // 只取前3个类名
                        if (classes.length > 0) {
                            return '.' + classes.join('.');
                        }
                    }
                    
                    // 构建路径选择器
                    let path = [];
                    let current = element;
                    
                    while (current && current.tagName && path.length < 5) {
                        let selector = current.tagName.toLowerCase();
                        
                        // 添加重要属性
                        if (current.type) selector += `[type="${current.type}"]`;
                        if (current.role) selector += `[role="${current.role}"]`;
                        
                        path.unshift(selector);
                        current = current.parentElement;
                    }
                    
                    return path.join(' > ');
                }
                
                console.log('🎬 录制脚本已注入！开始录制您的操作...');
                console.log('📋 可用命令:');
                console.log('  - window.recordedActions: 查看录制的操作');
                console.log('  - window.recordingEnabled = false: 停止录制');
                console.log('  - window.recordingEnabled = true: 开始录制');
            """)
            
            print("✅ 录制脚本已注入到页面")
            print("\n📋 现在请在比特浏览器中:")
            print("1. 执行完整的视频上传流程")
            print("2. 所有操作都会被自动录制")
            print("3. 完成后按回车键获取录制结果")
            
            input("\n按回车键获取录制结果...")
            
            # 获取录制的操作
            recorded_actions = await page.evaluate("window.recordedActions || []")
            
            if recorded_actions:
                print(f"\n✅ 录制完成！共录制了 {len(recorded_actions)} 个操作")
                
                # 保存录制结果
                import json
                with open('recorded_actions.json', 'w', encoding='utf-8') as f:
                    json.dump(recorded_actions, f, indent=2, ensure_ascii=False)
                
                print("📁 录制结果已保存到: recorded_actions.json")
                
                # 生成Python代码
                await generate_python_code(recorded_actions)
                
            else:
                print("❌ 未录制到任何操作")
            
            await page.close()
            await browser.close()
            
    except Exception as e:
        print(f"❌ 录制失败: {e}")
        import traceback
        traceback.print_exc()

async def generate_python_code(actions):
    """根据录制的操作生成Python代码"""
    print("\n🔧 生成Python代码...")
    
    code_lines = [
        "# 自动生成的YouTube上传代码",
        "import asyncio",
        "from playwright.async_api import async_playwright",
        "",
        "async def upload_video(page, video_path, title, description=''):",
        "    \"\"\"上传视频到YouTube\"\"\"",
        "    try:"
    ]
    
    for i, action in enumerate(actions):
        if action['action'] == 'click':
            code_lines.append(f"        # 步骤 {i+1}: 点击 {action.get('text', '元素')}")
            code_lines.append(f"        await page.click('{action['selector']}')")
            code_lines.append(f"        await page.wait_for_timeout(1000)")
            
        elif action['action'] == 'input':
            if 'file' in action.get('type', '').lower():
                code_lines.append(f"        # 步骤 {i+1}: 上传文件")
                code_lines.append(f"        await page.set_input_files('{action['selector']}', video_path)")
            else:
                code_lines.append(f"        # 步骤 {i+1}: 输入文本")
                if 'title' in action.get('selector', '').lower():
                    code_lines.append(f"        await page.fill('{action['selector']}', title)")
                elif 'description' in action.get('selector', '').lower():
                    code_lines.append(f"        await page.fill('{action['selector']}', description)")
                else:
                    code_lines.append(f"        await page.fill('{action['selector']}', '{action.get('value', '')}')")
            code_lines.append(f"        await page.wait_for_timeout(1000)")
            
        elif action['action'] == 'file_upload':
            code_lines.append(f"        # 步骤 {i+1}: 文件上传")
            code_lines.append(f"        await page.set_input_files('{action['selector']}', video_path)")
            code_lines.append(f"        await page.wait_for_timeout(2000)")
    
    code_lines.extend([
        "",
        "        print('✅ 视频上传完成')",
        "        return True",
        "",
        "    except Exception as e:",
        "        print(f'❌ 上传失败: {e}')",
        "        return False"
    ])
    
    # 保存生成的代码
    code_content = '\n'.join(code_lines)
    with open('generated_upload_code.py', 'w', encoding='utf-8') as f:
        f.write(code_content)
    
    print("📁 Python代码已生成: generated_upload_code.py")
    
    # 显示代码预览
    print("\n📄 生成的代码预览:")
    print("=" * 40)
    for i, line in enumerate(code_lines[:20]):
        print(f"{i+1:2d}: {line}")
    
    if len(code_lines) > 20:
        print(f"... (还有 {len(code_lines)-20} 行)")

if __name__ == "__main__":
    print("🎬 比特浏览器内录制工具")
    print("=" * 60)
    
    asyncio.run(record_in_bitbrowser())
