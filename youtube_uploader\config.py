"""
配置管理系统

实现配置文件读取、环境变量覆盖、默认配置管理等功能。
"""

import os
import yaml
import json
import shutil
from typing import Optional, Dict, Any, List, Union
from pathlib import Path
from datetime import datetime
import logging
from .models import ConfigModel, ValidationResult


# 配置常量
DEFAULT_CONFIG_FILENAME = "config.yml"
CONFIG_BACKUP_DIR = "config_backups"
ENV_PREFIX = "YOUTUBE_UPLOADER_"
SUPPORTED_CONFIG_FORMATS = ['.yml', '.yaml', '.json']


class ConfigError(Exception):
    """配置相关错误"""
    pass


class ConfigValidationError(ConfigError):
    """配置验证错误"""
    pass


class ConfigLoadError(ConfigError):
    """配置加载错误"""
    pass


class Config:
    """配置管理类"""

    def __init__(self, config_path: Optional[str] = None, auto_create_dirs: bool = True):
        """
        初始化配置

        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
            auto_create_dirs: 是否自动创建必要的目录
        """
        self.logger = logging.getLogger(__name__)
        self._config_path = config_path
        self._config_data = {}
        self._original_config_data = {}

        try:
            self._config_data = self._load_config(config_path)
            self._original_config_data = self._config_data.copy()
            self._apply_env_overrides()
            self.config = ConfigModel(**self._config_data)

            if auto_create_dirs:
                self.create_directories()

        except Exception as e:
            raise ConfigError(f"配置初始化失败: {e}") from e
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """加载配置文件"""
        # 获取默认配置
        default_config = self._get_default_config()

        # 如果没有提供配置文件路径，尝试查找默认配置文件
        if not config_path:
            config_path = self._find_default_config_file()

        # 如果找到配置文件，尝试加载
        if config_path and Path(config_path).exists():
            try:
                file_config = self._load_config_file(config_path)
                # 深度合并配置
                merged_config = self._deep_merge_config(default_config, file_config)
                self.logger.info(f"成功加载配置文件: {config_path}")
                return merged_config
            except Exception as e:
                error_msg = f"加载配置文件失败 {config_path}: {e}"
                self.logger.error(error_msg)
                raise ConfigLoadError(error_msg) from e
        else:
            if config_path:
                self.logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
            else:
                self.logger.info("未找到配置文件，使用默认配置")

        return default_config

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "concurrency": 1,
            "page_load_timeout": 30,
            "element_wait_timeout": 10,
            "upload_process_timeout": 300,
            "video_processing_timeout": 600,
            "max_retry_attempts": 3,
            "retry_delay_seconds": 3,
            "operation_interval_seconds": 10,
            "random_delay_min": 2,
            "random_delay_max": 5,
            "excel_sheet_name": "Tasks",
            "result_file": "upload_results.xlsx",
            "log_dir": "logs",
            "screenshot_dir": "screenshots",
            "save_screenshots": True,
            "save_page_html": False,
            "verbose_logging": True
        }

    def _find_default_config_file(self) -> Optional[str]:
        """查找默认配置文件"""
        possible_paths = [
            DEFAULT_CONFIG_FILENAME,
            f"./{DEFAULT_CONFIG_FILENAME}",
            f"./config/{DEFAULT_CONFIG_FILENAME}",
            f"./configs/{DEFAULT_CONFIG_FILENAME}"
        ]

        for path in possible_paths:
            if Path(path).exists():
                return path

        return None

    def _load_config_file(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        path = Path(config_path)

        if path.suffix not in SUPPORTED_CONFIG_FORMATS:
            raise ConfigLoadError(f"不支持的配置文件格式: {path.suffix}")

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if path.suffix == '.json':
                    return json.load(f)
                else:  # .yml or .yaml
                    return yaml.safe_load(f) or {}
        except yaml.YAMLError as e:
            raise ConfigLoadError(f"YAML格式错误: {e}") from e
        except json.JSONDecodeError as e:
            raise ConfigLoadError(f"JSON格式错误: {e}") from e
        except Exception as e:
            raise ConfigLoadError(f"读取文件失败: {e}") from e

    def _deep_merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并配置字典"""
        result = base.copy()

        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge_config(result[key], value)
            else:
                result[key] = value

        return result
    
    def _apply_env_overrides(self):
        """应用环境变量覆盖"""
        env_overrides = self._get_env_overrides()

        if env_overrides:
            self.logger.info(f"应用 {len(env_overrides)} 个环境变量覆盖")
            for key, value in env_overrides.items():
                self.logger.debug(f"环境变量覆盖: {key} = {value}")

            # 深度合并环境变量覆盖
            self._config_data = self._deep_merge_config(self._config_data, env_overrides)

    def _get_env_overrides(self) -> Dict[str, Any]:
        """获取环境变量覆盖"""
        overrides = {}

        for key, value in os.environ.items():
            if key.startswith(ENV_PREFIX):
                config_path = key[len(ENV_PREFIX):].lower()
                converted_value = self._convert_env_value(value)

                # 支持嵌套配置，如 YOUTUBE_UPLOADER_TIMEOUTS_PAGE_LOAD=60
                self._set_nested_value(overrides, config_path, converted_value)

        return overrides

    def _set_nested_value(self, config_dict: Dict[str, Any], path: str, value: Any):
        """设置嵌套配置值"""
        # 直接使用扁平化的键名，不进行嵌套
        config_dict[path] = value

    def _convert_env_value(self, value: str) -> Any:
        """转换环境变量值的数据类型"""
        # 空值
        if not value:
            return None

        # 布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'

        # None值
        if value.lower() in ('null', 'none'):
            return None

        # 列表值（逗号分隔）
        if ',' in value:
            return [self._convert_env_value(item.strip()) for item in value.split(',')]

        # 整数
        try:
            return int(value)
        except ValueError:
            pass

        # 浮点数
        try:
            return float(value)
        except ValueError:
            pass

        # 字符串
        return value
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return getattr(self.config, key, default)

    def set(self, key: str, value: Any):
        """设置配置值"""
        if hasattr(self.config, key):
            setattr(self.config, key, value)
            # 同时更新内部配置数据
            self._config_data[key] = value
        else:
            raise ConfigError(f"未知的配置项: {key}")

    def validate_config(self) -> ValidationResult:
        """验证配置"""
        result = ValidationResult(is_valid=True)

        try:
            # 验证目录权限
            self._validate_directories(result)

            # 验证文件路径
            self._validate_file_paths(result)

            # 验证配置逻辑
            self._validate_config_logic(result)

        except Exception as e:
            result.add_error(f"配置验证异常: {e}")

        return result

    def _validate_directories(self, result: ValidationResult):
        """验证目录"""
        directories = [
            ("日志目录", self.config.log_dir),
            ("截图目录", self.config.screenshot_dir)
        ]

        for name, directory in directories:
            try:
                Path(directory).mkdir(parents=True, exist_ok=True)
                # 测试写入权限
                test_file = Path(directory) / ".test_write"
                test_file.write_text("test")
                test_file.unlink()
            except Exception as e:
                result.add_error(f"{name} 无法创建或写入: {directory} - {e}")

    def _validate_file_paths(self, result: ValidationResult):
        """验证文件路径"""
        # 验证结果文件路径
        result_path = Path(self.config.result_file)
        if result_path.exists() and not os.access(result_path, os.W_OK):
            result.add_error(f"结果文件无写入权限: {self.config.result_file}")

    def _validate_config_logic(self, result: ValidationResult):
        """验证配置逻辑"""
        # 验证超时设置的合理性
        if self.config.element_wait_timeout > self.config.page_load_timeout:
            result.add_warning("元素等待超时大于页面加载超时，可能导致不必要的等待")

        # 验证重试设置
        if self.config.max_retry_attempts > 5:
            result.add_warning("重试次数过多可能导致程序运行时间过长")

        # 验证并发设置
        if self.config.concurrency > 5:
            result.add_warning("并发数过高可能导致系统资源不足或触发平台限制")

    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.config.log_dir,
            self.config.screenshot_dir,
            CONFIG_BACKUP_DIR
        ]

        for directory in directories:
            try:
                Path(directory).mkdir(parents=True, exist_ok=True)
                self.logger.debug(f"创建目录: {directory}")
            except Exception as e:
                self.logger.error(f"创建目录失败 {directory}: {e}")
                raise ConfigError(f"无法创建目录 {directory}: {e}") from e
    
    def backup_config(self, backup_name: Optional[str] = None) -> str:
        """备份当前配置"""
        if not self._config_path:
            raise ConfigError("无法备份配置：未指定配置文件路径")

        if not Path(self._config_path).exists():
            raise ConfigError(f"配置文件不存在: {self._config_path}")

        # 创建备份目录
        backup_dir = Path(CONFIG_BACKUP_DIR)
        backup_dir.mkdir(parents=True, exist_ok=True)

        # 生成备份文件名
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"config_backup_{timestamp}.yml"

        backup_path = backup_dir / backup_name

        # 复制配置文件
        shutil.copy2(self._config_path, backup_path)

        self.logger.info(f"配置已备份到: {backup_path}")
        return str(backup_path)

    def restore_config(self, backup_path: str):
        """从备份恢复配置"""
        backup_file = Path(backup_path)

        if not backup_file.exists():
            raise ConfigError(f"备份文件不存在: {backup_path}")

        if not self._config_path:
            raise ConfigError("无法恢复配置：未指定配置文件路径")

        # 备份当前配置
        current_backup = self.backup_config("before_restore")

        try:
            # 恢复配置文件
            shutil.copy2(backup_path, self._config_path)

            # 重新加载配置
            self._config_data = self._load_config(self._config_path)
            self._apply_env_overrides()
            self.config = ConfigModel(**self._config_data)

            self.logger.info(f"配置已从备份恢复: {backup_path}")

        except Exception as e:
            # 恢复失败，回滚到之前的配置
            shutil.copy2(current_backup, self._config_path)
            raise ConfigError(f"配置恢复失败: {e}") from e

    def list_backups(self) -> List[str]:
        """列出所有配置备份"""
        backup_dir = Path(CONFIG_BACKUP_DIR)

        if not backup_dir.exists():
            return []

        backups = []
        for file in backup_dir.glob("*.yml"):
            backups.append(str(file))

        return sorted(backups, reverse=True)  # 按时间倒序

    def reset_to_default(self):
        """重置为默认配置"""
        self._config_data = self._get_default_config()
        self._apply_env_overrides()
        self.config = ConfigModel(**self._config_data)

        self.logger.info("配置已重置为默认值")

    def save_current_config(self, output_path: Optional[str] = None):
        """保存当前配置到文件"""
        if not output_path:
            output_path = self._config_path or DEFAULT_CONFIG_FILENAME

        # 将当前配置转换为字典
        config_dict = self.config.model_dump()

        # 重新组织配置结构，使其更易读
        organized_config = self._organize_config_for_save(config_dict)

        # 保存到文件
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(organized_config, f, default_flow_style=False, allow_unicode=True, indent=2)

        self.logger.info(f"当前配置已保存到: {output_path}")

    def _organize_config_for_save(self, config_dict: Dict[str, Any]) -> Dict[str, Any]:
        """重新组织配置结构以便保存"""
        return {
            "# 并发设置": None,
            "concurrency": config_dict.get("concurrency"),

            "# 超时设置（秒）": None,
            "timeouts": {
                "page_load": config_dict.get("page_load_timeout"),
                "element_wait": config_dict.get("element_wait_timeout"),
                "upload_process": config_dict.get("upload_process_timeout"),
                "video_processing": config_dict.get("video_processing_timeout")
            },

            "# 重试设置": None,
            "retry": {
                "max_attempts": config_dict.get("max_retry_attempts"),
                "delay_seconds": config_dict.get("retry_delay_seconds")
            },

            "# 操作设置": None,
            "operation": {
                "interval_seconds": config_dict.get("operation_interval_seconds"),
                "random_delay_min": config_dict.get("random_delay_min"),
                "random_delay_max": config_dict.get("random_delay_max")
            },

            "# 文件设置": None,
            "files": {
                "excel_sheet_name": config_dict.get("excel_sheet_name"),
                "result_file": config_dict.get("result_file"),
                "log_dir": config_dict.get("log_dir"),
                "screenshot_dir": config_dict.get("screenshot_dir")
            },

            "# 调试设置": None,
            "debug": {
                "save_screenshots": config_dict.get("save_screenshots"),
                "save_page_html": config_dict.get("save_page_html"),
                "verbose_logging": config_dict.get("verbose_logging")
            }
        }

    def save_default_config(self, output_path: str = DEFAULT_CONFIG_FILENAME):
        """保存默认配置到文件"""
        default_config = self._get_default_config()
        organized_config = self._organize_config_for_save(default_config)

        # 清理注释键（以#开头的键）
        clean_config = {k: v for k, v in organized_config.items() if v is not None and not k.startswith('#')}

        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(clean_config, f, default_flow_style=False, allow_unicode=True, indent=2)

        self.logger.info(f"默认配置已保存到: {output_path}")
        print(f"默认配置已保存到: {output_path}")

    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            "config_path": self._config_path,
            "config_exists": bool(self._config_path and Path(self._config_path).exists()),
            "env_overrides_count": len(self._get_env_overrides()),
            "validation_result": self.validate_config().model_dump(),
            "backup_count": len(self.list_backups()),
            "config_summary": {
                "concurrency": self.config.concurrency,
                "timeouts": self.config.get_timeout_dict(),
                "retry": self.config.get_retry_config(),
                "operation": self.config.get_operation_config()
            }
        }

    def print_config_summary(self):
        """打印配置摘要"""
        info = self.get_config_info()

        print("=" * 50)
        print("配置摘要")
        print("=" * 50)
        print(f"配置文件: {info['config_path'] or '使用默认配置'}")
        print(f"文件存在: {'是' if info['config_exists'] else '否'}")
        print(f"环境变量覆盖: {info['env_overrides_count']} 个")
        print(f"配置备份: {info['backup_count']} 个")

        print("\n核心配置:")
        summary = info['config_summary']
        print(f"  并发数: {summary['concurrency']}")
        print(f"  页面加载超时: {summary['timeouts']['page_load']}秒")
        print(f"  最大重试次数: {summary['retry']['max_attempts']}")
        print(f"  操作间隔: {summary['operation']['interval_seconds']}秒")
        print(f"  随机延迟: {summary['operation']['random_delay_min']}-{summary['operation']['random_delay_max']}秒")

        validation = ValidationResult(**info['validation_result'])
        print(f"\n配置验证: {validation.get_summary()}")

        if validation.errors:
            print("错误:")
            for error in validation.errors:
                print(f"  ❌ {error}")

        if validation.warnings:
            print("警告:")
            for warning in validation.warnings:
                print(f"  ⚠️ {warning}")

        print("=" * 50)

    def export_config(self, format: str = 'yaml', output_path: Optional[str] = None) -> str:
        """导出配置"""
        if format not in ['yaml', 'json']:
            raise ConfigError(f"不支持的导出格式: {format}")

        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"config_export_{timestamp}.{format}"

        config_dict = self.config.model_dump()

        if format == 'json':
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
        else:  # yaml
            organized_config = self._organize_config_for_save(config_dict)
            clean_config = {k: v for k, v in organized_config.items() if v is not None and not k.startswith('#')}
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(clean_config, f, default_flow_style=False, allow_unicode=True, indent=2)

        self.logger.info(f"配置已导出到: {output_path}")
        return output_path

    def import_config(self, config_path: str, backup_current: bool = True):
        """导入配置"""
        if not Path(config_path).exists():
            raise ConfigError(f"配置文件不存在: {config_path}")

        # 备份当前配置
        if backup_current and self._config_path:
            self.backup_config("before_import")

        try:
            # 加载新配置
            new_config_data = self._load_config_file(config_path)

            # 验证新配置
            test_config = ConfigModel(**new_config_data)

            # 应用新配置
            self._config_data = new_config_data
            self._apply_env_overrides()
            self.config = test_config

            self.logger.info(f"配置已导入: {config_path}")

        except Exception as e:
            raise ConfigError(f"配置导入失败: {e}") from e


def load_config(config_path: Optional[str] = None) -> Config:
    """
    加载配置的便捷函数
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Config: 配置对象
    """
    return Config(config_path)
