"""
主程序入口

实现命令行参数解析、主程序入口、进度显示等用户交互功能。
"""

import argparse
import asyncio
import sys
import time
from pathlib import Path
from typing import Optional, List
import logging

from .config import load_config
from .excel_handler import ExcelHandler
from .executor import TaskExecutor
from .models import TaskModel, ResultModel
from .utils import ensure_directory, get_timestamp


class YouTubeUploaderRunner:
    """YouTube 上传器主程序"""
    
    def __init__(self):
        """初始化主程序"""
        self.logger = self._setup_logging()
        self.config = None
        self.excel_handler = None
        self.executor = None
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        # 创建日志目录
        log_dir = ensure_directory("logs")
        
        # 配置日志格式
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        # 配置根日志记录器
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                # 控制台输出
                logging.StreamHandler(sys.stdout),
                # 文件输出
                logging.FileHandler(
                    log_dir / f"youtube_uploader_{get_timestamp()}.log",
                    encoding='utf-8'
                )
            ]
        )
        
        return logging.getLogger(__name__)
    
    def parse_arguments(self) -> argparse.Namespace:
        """解析命令行参数"""
        parser = argparse.ArgumentParser(
            description="YouTube Shorts 批量上传自动化工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例用法:
  python -m youtube_uploader.runner --excel tasks.xlsx
  python -m youtube_uploader.runner --excel tasks.xlsx --concurrency 2
  python -m youtube_uploader.runner --excel tasks.xlsx --config custom_config.yml
  python -m youtube_uploader.runner --excel tasks.xlsx --profiles "profile1,profile2"
  python -m youtube_uploader.runner --create-sample
            """
        )
        
        parser.add_argument(
            "--excel", "-e",
            type=str,
            help="Excel 任务文件路径"
        )
        
        parser.add_argument(
            "--config", "-c",
            type=str,
            help="配置文件路径 (可选)"
        )
        
        parser.add_argument(
            "--concurrency",
            type=int,
            help="并发数 (覆盖配置文件设置)"
        )
        
        parser.add_argument(
            "--profiles",
            type=str,
            help="指定要处理的 profile 列表，用逗号分隔"
        )
        
        parser.add_argument(
            "--create-sample",
            action="store_true",
            help="创建示例 Excel 文件"
        )
        
        parser.add_argument(
            "--create-config",
            action="store_true",
            help="创建默认配置文件"
        )
        
        parser.add_argument(
            "--validate-only",
            action="store_true",
            help="仅验证任务文件，不执行上传"
        )
        
        return parser.parse_args()
    
    async def run(self):
        """主运行方法"""
        try:
            # 解析命令行参数
            args = self.parse_arguments()
            
            # 处理特殊命令
            if args.create_sample:
                self._create_sample_excel()
                return
            
            if args.create_config:
                self._create_default_config()
                return
            
            # 检查必需参数
            if not args.excel:
                self.logger.error("请提供 Excel 文件路径 (--excel)")
                sys.exit(1)
            
            # 加载配置
            self.config = load_config(args.config)
            
            # 应用命令行参数覆盖
            if args.concurrency:
                self.config.config.concurrency = args.concurrency
            
            # 创建必要目录
            self.config.create_directories()
            
            # 初始化组件
            self.excel_handler = ExcelHandler(self.config.config.excel_sheet_name)
            self.executor = TaskExecutor(self.config.config.dict())
            
            # 读取任务
            tasks = self._load_tasks(args.excel)
            
            # 过滤 profile（如果指定）
            if args.profiles:
                tasks = self._filter_tasks_by_profiles(tasks, args.profiles)
            
            # 验证任务
            self._validate_tasks(tasks)
            
            # 仅验证模式
            if args.validate_only:
                self.logger.info("验证完成，退出程序")
                return
            
            # 执行任务
            results = await self._execute_tasks(tasks)
            
            # 保存结果
            self._save_results(results)
            
            # 显示总结
            self._show_summary(results)
            
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
            sys.exit(1)
        except Exception as e:
            self.logger.error(f"程序执行失败: {e}")
            sys.exit(1)
    
    def _create_sample_excel(self):
        """创建示例 Excel 文件"""
        self.logger.info("创建示例 Excel 文件...")
        excel_handler = ExcelHandler()
        excel_handler.create_sample_excel("sample_tasks.xlsx")
        self.logger.info("示例文件已创建: sample_tasks.xlsx")
    
    def _create_default_config(self):
        """创建默认配置文件"""
        self.logger.info("创建默认配置文件...")
        config = load_config()
        config.save_default_config("config.yml")
    
    def _load_tasks(self, excel_path: str) -> List[TaskModel]:
        """加载任务"""
        self.logger.info(f"加载任务文件: {excel_path}")
        
        if not Path(excel_path).exists():
            raise FileNotFoundError(f"Excel 文件不存在: {excel_path}")
        
        tasks = self.excel_handler.read_tasks(excel_path)
        self.logger.info(f"成功加载 {len(tasks)} 个任务")
        
        return tasks
    
    def _filter_tasks_by_profiles(self, tasks: List[TaskModel], profiles_str: str) -> List[TaskModel]:
        """根据指定的 profile 过滤任务"""
        specified_profiles = [p.strip() for p in profiles_str.split(",")]
        filtered_tasks = [task for task in tasks if task.profile_name in specified_profiles]
        
        self.logger.info(f"根据指定 profile 过滤任务: {specified_profiles}")
        self.logger.info(f"过滤后剩余 {len(filtered_tasks)} 个任务")
        
        return filtered_tasks
    
    def _validate_tasks(self, tasks: List[TaskModel]):
        """验证任务"""
        self.logger.info("验证任务数据...")
        
        errors = self.excel_handler.validate_tasks(tasks)
        
        if errors:
            self.logger.error("任务验证失败:")
            for error in errors:
                self.logger.error(f"  - {error}")
            raise ValueError("任务验证失败，请检查数据")
        
        self.logger.info("任务验证通过")
    
    async def _execute_tasks(self, tasks: List[TaskModel]) -> List[ResultModel]:
        """执行任务"""
        self.logger.info("开始执行任务...")
        start_time = time.time()
        
        results = await self.executor.execute_tasks(tasks)
        
        total_time = time.time() - start_time
        self.logger.info(f"任务执行完成，总耗时: {total_time:.1f}秒")
        
        return results
    
    def _save_results(self, results: List[ResultModel]):
        """保存结果"""
        result_file = f"results_{get_timestamp()}.xlsx"
        self.logger.info(f"保存结果到: {result_file}")
        
        self.excel_handler.write_results(results, result_file)
    
    def _show_summary(self, results: List[ResultModel]):
        """显示执行总结"""
        total = len(results)
        success = len([r for r in results if r.status == "success"])
        failed = len([r for r in results if r.status == "failed"])
        needs_verification = len([r for r in results if r.status == "needs_verification"])
        
        self.logger.info("=" * 50)
        self.logger.info("执行总结:")
        self.logger.info(f"  总任务数: {total}")
        self.logger.info(f"  成功: {success}")
        self.logger.info(f"  失败: {failed}")
        self.logger.info(f"  需要验证: {needs_verification}")
        self.logger.info(f"  成功率: {success/total*100:.1f}%" if total > 0 else "  成功率: 0%")
        self.logger.info("=" * 50)


def main():
    """主函数"""
    runner = YouTubeUploaderRunner()
    asyncio.run(runner.run())


if __name__ == "__main__":
    main()
