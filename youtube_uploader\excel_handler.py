"""
Excel 处理模块

实现 Excel 任务清单读取、数据验证、结果写入等功能，支持多种格式。
"""

from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import logging
from datetime import datetime
import uuid
import re
import csv

# pandas暂时不可用，使用标准库替代
PANDAS_AVAILABLE = False

from .models import TaskModel, ResultModel, TaskStatus, ValidationResult, ProgressModel


# 异常类定义
class ExcelError(Exception):
    """Excel处理相关错误基类"""
    pass


class ExcelFileError(ExcelError):
    """Excel文件错误"""
    pass


class ExcelDataError(ExcelError):
    """Excel数据错误"""
    pass


class ExcelValidationError(ExcelError):
    """Excel验证错误"""
    pass


# 常量定义
SUPPORTED_EXCEL_EXTENSIONS = ['.xlsx', '.xls']
SUPPORTED_CSV_EXTENSIONS = ['.csv']
SUPPORTED_EXTENSIONS = SUPPORTED_EXCEL_EXTENSIONS + SUPPORTED_CSV_EXTENSIONS

# Excel样式常量
HEADER_FONT = Font(bold=True, color="FFFFFF")
HEADER_FILL = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
SUCCESS_FILL = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
ERROR_FILL = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")
WARNING_FILL = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")

# 数据验证规则
VISIBILITY_OPTIONS = ["public", "unlisted", "private", "scheduled"]
LANGUAGE_OPTIONS = ["zh-CN", "zh-TW", "en-US", "en-GB", "ja-JP", "ko-KR"]


class ExcelHandler:
    """Excel 文件处理器"""

    def __init__(self, sheet_name: str = "Tasks", enable_validation: bool = True):
        """
        初始化 Excel 处理器

        Args:
            sheet_name: 工作表名称
            enable_validation: 是否启用数据验证
        """
        self.logger = logging.getLogger(__name__)
        self.sheet_name = sheet_name
        self.enable_validation = enable_validation
        self._task_cache: Dict[str, List[TaskModel]] = {}
        self._validation_cache: Dict[str, ValidationResult] = {}

    def _get_file_extension(self, file_path: str) -> str:
        """获取文件扩展名"""
        return Path(file_path).suffix.lower()

    def _is_supported_file(self, file_path: str) -> bool:
        """检查是否为支持的文件格式"""
        return self._get_file_extension(file_path) in SUPPORTED_EXTENSIONS

    def _detect_encoding(self, file_path: str) -> str:
        """检测CSV文件编码"""
        try:
            import chardet
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB
                result = chardet.detect(raw_data)
                return result.get('encoding', 'utf-8')
        except ImportError:
            # 如果没有chardet，尝试常见编码
            for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        f.read(1000)  # 尝试读取一部分
                    return encoding
                except UnicodeDecodeError:
                    continue
            return 'utf-8'  # 默认返回utf-8
        except Exception:
            return 'utf-8'
    
    def read_tasks(self, file_path: str, use_cache: bool = True) -> List[TaskModel]:
        """
        从文件读取任务清单（支持Excel和CSV）

        Args:
            file_path: 文件路径
            use_cache: 是否使用缓存

        Returns:
            List[TaskModel]: 任务列表

        Raises:
            ExcelFileError: 文件相关错误
            ExcelDataError: 数据相关错误
        """
        try:
            # 检查缓存
            if use_cache and file_path in self._task_cache:
                self.logger.info(f"从缓存读取任务: {file_path}")
                return self._task_cache[file_path]

            self.logger.info(f"开始读取文件: {file_path}")

            # 检查文件是否存在
            if not Path(file_path).exists():
                raise ExcelFileError(f"文件不存在: {file_path}")

            # 检查文件格式
            if not self._is_supported_file(file_path):
                raise ExcelFileError(f"不支持的文件格式: {self._get_file_extension(file_path)}")

            # 根据文件类型选择读取方法
            file_ext = self._get_file_extension(file_path)
            if file_ext in SUPPORTED_EXCEL_EXTENSIONS:
                tasks = self._read_excel_tasks(file_path)
            elif file_ext in SUPPORTED_CSV_EXTENSIONS:
                tasks = self._read_csv_tasks(file_path)
            else:
                raise ExcelFileError(f"不支持的文件格式: {file_ext}")

            # 缓存结果
            if use_cache:
                self._task_cache[file_path] = tasks

            self.logger.info(f"成功解析 {len(tasks)} 个有效任务")
            return tasks

        except (ExcelFileError, ExcelDataError):
            raise
        except Exception as e:
            self.logger.error(f"读取文件失败: {e}")
            raise ExcelFileError(f"读取文件失败: {e}") from e

    def _read_excel_tasks(self, excel_path: str) -> List[TaskModel]:
        """从Excel文件读取任务"""
        try:
            # 读取 Excel 文件
            workbook = load_workbook(excel_path, data_only=True)

            # 检查工作表是否存在
            if self.sheet_name not in workbook.sheetnames:
                available_sheets = ", ".join(workbook.sheetnames)
                raise ExcelDataError(f"工作表 '{self.sheet_name}' 不存在。可用工作表: {available_sheets}")

            worksheet = workbook[self.sheet_name]

            # 获取表头
            headers = self._extract_headers(worksheet)
            self.logger.info(f"发现表头: {headers}")

            # 验证必需的表头
            self._validate_headers(headers)

            # 读取数据行
            tasks = []
            error_rows = []

            for row_num in range(2, worksheet.max_row + 1):
                try:
                    row_data = self._extract_row_data(worksheet, row_num, headers)

                    # 跳过空行
                    if not any(v for v in row_data.values() if v is not None):
                        continue

                    # 数据类型转换
                    row_data = self._convert_data_types(row_data)

                    # 创建任务模型
                    task = TaskModel(**row_data)
                    tasks.append(task)

                except Exception as e:
                    error_msg = f"第 {row_num} 行数据验证失败: {e}"
                    self.logger.error(error_msg)
                    error_rows.append((row_num, str(e)))
                    continue

            # 如果有错误行，记录详细信息
            if error_rows:
                self.logger.warning(f"共有 {len(error_rows)} 行数据验证失败")
                for row_num, error in error_rows[:5]:  # 只显示前5个错误
                    self.logger.warning(f"  行 {row_num}: {error}")
                if len(error_rows) > 5:
                    self.logger.warning(f"  ... 还有 {len(error_rows) - 5} 个错误")

            return tasks

        except ExcelDataError:
            raise
        except Exception as e:
            raise ExcelDataError(f"读取Excel文件失败: {e}") from e

    def _read_csv_tasks(self, csv_path: str) -> List[TaskModel]:
        """从CSV文件读取任务"""
        try:
            # 检测编码
            encoding = self._detect_encoding(csv_path)
            self.logger.info(f"检测到CSV编码: {encoding}")

            # 使用标准库读取CSV文件
            tasks = []
            error_rows = []

            with open(csv_path, 'r', encoding=encoding, newline='') as csvfile:
                reader = csv.DictReader(csvfile)

                for row_num, row_data in enumerate(reader, 2):
                    try:
                        # 处理空值
                        cleaned_data = {}
                        for key, value in row_data.items():
                            if value is None or value.strip() == '':
                                cleaned_data[key] = None
                            else:
                                cleaned_data[key] = value.strip()

                        # 跳过空行
                        if not any(v for v in cleaned_data.values() if v is not None):
                            continue

                        # 数据类型转换
                        cleaned_data = self._convert_data_types(cleaned_data)

                        # 创建任务模型
                        task = TaskModel(**cleaned_data)
                        tasks.append(task)

                    except Exception as e:
                        error_msg = f"第 {row_num} 行数据验证失败: {e}"
                        self.logger.error(error_msg)
                        error_rows.append((row_num, str(e)))
                        continue

            # 记录错误信息
            if error_rows:
                self.logger.warning(f"CSV文件共有 {len(error_rows)} 行数据验证失败")

            return tasks

        except Exception as e:
            raise ExcelDataError(f"读取CSV文件失败: {e}") from e

    def _extract_headers(self, worksheet) -> List[str]:
        """提取表头"""
        headers = []
        for cell in worksheet[1]:
            if cell.value:
                # 清理表头名称
                header = str(cell.value).strip()
                headers.append(header)
            else:
                break
        return headers

    def _validate_headers(self, headers: List[str]):
        """验证表头"""
        required_headers = [
            'profile_name', 'video_path', 'title', 'visibility',
            'audience_made_for_kids', 'age_restriction_18plus',
            'allow_remix', 'language'
        ]

        missing_headers = [h for h in required_headers if h not in headers]
        if missing_headers:
            raise ExcelDataError(f"缺少必需的表头: {', '.join(missing_headers)}")

    def _extract_row_data(self, worksheet, row_num: int, headers: List[str]) -> Dict[str, Any]:
        """提取行数据"""
        row_data = {}
        for col_num, header in enumerate(headers, 1):
            cell = worksheet.cell(row=row_num, column=col_num)
            cell_value = cell.value

            # 处理空值和空字符串
            if cell_value is not None:
                if isinstance(cell_value, str):
                    cell_value = cell_value.strip()
                    if cell_value == '':
                        cell_value = None
                row_data[header] = cell_value
            else:
                row_data[header] = None

        return row_data

    def _convert_data_types(self, row_data: Dict[str, Any]) -> Dict[str, Any]:
        """转换数据类型"""
        converted_data = row_data.copy()

        # 布尔值转换
        bool_fields = ['audience_made_for_kids', 'age_restriction_18plus', 'allow_remix']
        for field in bool_fields:
            if field in converted_data and converted_data[field] is not None:
                value = converted_data[field]
                if isinstance(value, str):
                    value = value.lower()
                    if value in ('true', '是', 'yes', '1'):
                        converted_data[field] = True
                    elif value in ('false', '否', 'no', '0'):
                        converted_data[field] = False
                    else:
                        # 保持原值，让Pydantic验证器处理
                        pass
                elif isinstance(value, (int, float)):
                    converted_data[field] = bool(value)

        # 字符串字段清理
        string_fields = ['profile_name', 'video_path', 'title', 'description', 'tags',
                        'visibility', 'schedule_time', 'playlist', 'language', 'thumbnail_path', 'note']
        for field in string_fields:
            if field in converted_data and converted_data[field] is not None:
                if not isinstance(converted_data[field], str):
                    converted_data[field] = str(converted_data[field])
                converted_data[field] = converted_data[field].strip()
                if converted_data[field] == '':
                    converted_data[field] = None

        return converted_data
    
    def write_results(self, results: List[ResultModel], output_path: str,
                     include_summary: bool = True, apply_formatting: bool = True) -> str:
        """
        将结果写入文件（支持Excel和CSV）

        Args:
            results: 结果列表
            output_path: 输出文件路径
            include_summary: 是否包含摘要信息
            apply_formatting: 是否应用格式化（仅Excel）

        Returns:
            str: 实际输出文件路径

        Raises:
            ExcelError: 写入失败时抛出
        """
        try:
            self.logger.info(f"开始写入结果到文件: {output_path}")

            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 根据文件扩展名选择写入方法
            file_ext = self._get_file_extension(output_path)

            if file_ext in SUPPORTED_EXCEL_EXTENSIONS:
                actual_path = self._write_excel_results(results, output_path, include_summary, apply_formatting)
            elif file_ext in SUPPORTED_CSV_EXTENSIONS:
                actual_path = self._write_csv_results(results, output_path)
            else:
                # 默认使用Excel格式
                if not output_path.endswith(('.xlsx', '.csv')):
                    output_path += '.xlsx'
                actual_path = self._write_excel_results(results, output_path, include_summary, apply_formatting)

            self.logger.info(f"成功写入 {len(results)} 条结果到: {actual_path}")
            return actual_path

        except Exception as e:
            self.logger.error(f"写入结果文件失败: {e}")
            raise ExcelError(f"写入结果文件失败: {e}") from e

    def _write_excel_results(self, results: List[ResultModel], output_path: str,
                           include_summary: bool, apply_formatting: bool) -> str:
        """写入Excel结果文件"""
        workbook = Workbook()

        # 写入结果数据
        self._write_results_sheet(workbook, results, apply_formatting)

        # 写入摘要信息
        if include_summary and results:
            self._write_summary_sheet(workbook, results)

        # 删除默认工作表（如果有其他工作表）
        if len(workbook.worksheets) > 1 and "Sheet" in [ws.title for ws in workbook.worksheets]:
            default_sheet = workbook["Sheet"]
            workbook.remove(default_sheet)

        # 保存文件
        workbook.save(output_path)
        return output_path

    def _write_csv_results(self, results: List[ResultModel], output_path: str) -> str:
        """写入CSV结果文件"""
        if not results:
            # 创建空文件
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['No results'])
            return output_path

        # 获取字段名
        fieldnames = list(results[0].model_dump().keys())

        # 写入CSV文件
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in results:
                result_dict = result.model_dump()
                # 格式化时间字段
                for field in ['started_at', 'finished_at']:
                    if result_dict.get(field):
                        result_dict[field] = result_dict[field].strftime('%Y-%m-%d %H:%M:%S')
                writer.writerow(result_dict)

        return output_path

    def _write_results_sheet(self, workbook: Workbook, results: List[ResultModel], apply_formatting: bool):
        """写入结果工作表"""
        worksheet = workbook.active
        worksheet.title = "Results"

        if not results:
            worksheet.cell(row=1, column=1, value="No results")
            return

        # 获取表头
        headers = list(results[0].model_dump().keys())

        # 写入表头
        for col_num, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_num, value=header)
            if apply_formatting:
                cell.font = HEADER_FONT
                cell.fill = HEADER_FILL
                cell.alignment = Alignment(horizontal='center', vertical='center')

        # 写入数据
        for row_num, result in enumerate(results, 2):
            result_dict = result.model_dump()
            for col_num, header in enumerate(headers, 1):
                value = result_dict[header]

                # 格式化时间字段
                if header in ['started_at', 'finished_at'] and value:
                    value = value.strftime('%Y-%m-%d %H:%M:%S')

                cell = worksheet.cell(row=row_num, column=col_num, value=value)

                # 应用条件格式
                if apply_formatting and header == 'status':
                    if value == 'success':
                        cell.fill = SUCCESS_FILL
                    elif value == 'failed':
                        cell.fill = ERROR_FILL
                    elif value == 'needs_verification':
                        cell.fill = WARNING_FILL

        # 调整列宽
        if apply_formatting:
            self._auto_adjust_column_width(worksheet)

    def _write_summary_sheet(self, workbook: Workbook, results: List[ResultModel]):
        """写入摘要工作表"""
        summary_sheet = workbook.create_sheet(title="Summary")

        # 计算统计信息
        total_count = len(results)
        success_count = len([r for r in results if r.status == TaskStatus.SUCCESS])
        failed_count = len([r for r in results if r.status == TaskStatus.FAILED])
        verification_count = len([r for r in results if r.status == TaskStatus.NEEDS_VERIFICATION])

        # 计算时间统计
        durations = [r.duration_seconds for r in results if r.duration_seconds is not None]
        avg_duration = sum(durations) / len(durations) if durations else 0
        max_duration = max(durations) if durations else 0
        min_duration = min(durations) if durations else 0

        # 写入摘要信息
        summary_data = [
            ["执行摘要", ""],
            ["", ""],
            ["总任务数", total_count],
            ["成功任务数", success_count],
            ["失败任务数", failed_count],
            ["需要验证任务数", verification_count],
            ["成功率", f"{success_count/total_count*100:.1f}%" if total_count > 0 else "0%"],
            ["", ""],
            ["时间统计", ""],
            ["平均执行时间", f"{avg_duration:.1f}秒" if avg_duration > 0 else "N/A"],
            ["最长执行时间", f"{max_duration:.1f}秒" if max_duration > 0 else "N/A"],
            ["最短执行时间", f"{min_duration:.1f}秒" if min_duration > 0 else "N/A"],
            ["", ""],
            ["生成时间", datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
        ]

        for row_num, (label, value) in enumerate(summary_data, 1):
            summary_sheet.cell(row=row_num, column=1, value=label)
            summary_sheet.cell(row=row_num, column=2, value=value)

            # 格式化标题行
            if label in ["执行摘要", "时间统计"]:
                for col in [1, 2]:
                    cell = summary_sheet.cell(row=row_num, column=col)
                    cell.font = Font(bold=True, size=14)
                    cell.fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")

        # 调整列宽
        summary_sheet.column_dimensions['A'].width = 20
        summary_sheet.column_dimensions['B'].width = 15

    def _auto_adjust_column_width(self, worksheet):
        """自动调整列宽"""
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)

            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            adjusted_width = min(max_length + 2, 50)  # 最大宽度50
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def create_sample_excel(self, output_path: str = "sample_tasks.xlsx",
                          include_validation: bool = True, include_comments: bool = True) -> str:
        """
        创建示例 Excel 文件

        Args:
            output_path: 输出文件路径
            include_validation: 是否包含数据验证
            include_comments: 是否包含注释说明

        Returns:
            str: 创建的文件路径
        """
        try:
            self.logger.info(f"创建示例 Excel 文件: {output_path}")

            # 示例数据
            sample_data = [
                {
                    'profile_name': 'profile1',
                    'video_path': 'C:/videos/sample1.mp4',
                    'title': '示例视频标题1',
                    'description': '这是一个示例视频描述',
                    'tags': '教程|Python|自动化',
                    'visibility': 'unlisted',
                    'schedule_time': '',
                    'audience_made_for_kids': False,
                    'age_restriction_18plus': False,
                    'playlist': '',
                    'allow_remix': True,
                    'language': 'zh-CN',
                    'thumbnail_path': '',
                    'note': '示例备注'
                },
                {
                    'profile_name': 'profile2',
                    'video_path': 'C:/videos/sample2.mp4',
                    'title': '示例视频标题2',
                    'description': '这是另一个示例视频描述',
                    'tags': '娱乐|搞笑',
                    'visibility': 'scheduled',
                    'schedule_time': '2025-08-20 14:30:00',
                    'audience_made_for_kids': False,
                    'age_restriction_18plus': False,
                    'playlist': '我的播放列表',
                    'allow_remix': True,
                    'language': 'en-US',
                    'thumbnail_path': 'C:/thumbnails/thumb2.jpg',
                    'note': '定时发布示例'
                }
            ]

            # 创建工作簿
            workbook = Workbook()
            worksheet = workbook.active
            worksheet.title = self.sheet_name

            # 写入表头
            headers = list(sample_data[0].keys())
            for col_num, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col_num, value=header)
                cell.font = HEADER_FONT
                cell.fill = HEADER_FILL
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # 写入数据
            for row_num, data in enumerate(sample_data, 2):
                for col_num, header in enumerate(headers, 1):
                    worksheet.cell(row=row_num, column=col_num, value=data[header])

            # 添加数据验证
            if include_validation:
                self._add_data_validation(worksheet, len(sample_data) + 10)  # 预留10行

            # 添加注释说明
            if include_comments:
                self._add_comments_sheet(workbook)

            # 调整列宽
            self._auto_adjust_column_width(worksheet)

            # 保存文件
            workbook.save(output_path)
            self.logger.info(f"示例 Excel 文件已创建: {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"创建示例 Excel 文件失败: {e}")
            raise ExcelError(f"创建示例文件失败: {e}") from e

    def _add_data_validation(self, worksheet, max_row: int):
        """添加数据验证"""
        # 可见性验证
        visibility_validation = DataValidation(
            type="list",
            formula1=f'"{",".join(VISIBILITY_OPTIONS)}"',
            showErrorMessage=True,
            errorTitle="无效的可见性选项",
            error="请选择: " + ", ".join(VISIBILITY_OPTIONS)
        )
        worksheet.add_data_validation(visibility_validation)
        visibility_validation.add(f'F2:F{max_row}')  # visibility列

        # 语言验证
        language_validation = DataValidation(
            type="list",
            formula1=f'"{",".join(LANGUAGE_OPTIONS)}"',
            showErrorMessage=True,
            errorTitle="无效的语言选项",
            error="请选择: " + ", ".join(LANGUAGE_OPTIONS)
        )
        worksheet.add_data_validation(language_validation)
        language_validation.add(f'L2:L{max_row}')  # language列

        # 布尔值验证
        bool_validation = DataValidation(
            type="list",
            formula1='"TRUE,FALSE"',
            showErrorMessage=True,
            errorTitle="无效的布尔值",
            error="请选择: TRUE 或 FALSE"
        )
        worksheet.add_data_validation(bool_validation)
        bool_validation.add(f'H2:H{max_row}')  # audience_made_for_kids列
        bool_validation.add(f'I2:I{max_row}')  # age_restriction_18plus列
        bool_validation.add(f'K2:K{max_row}')  # allow_remix列

    def _add_comments_sheet(self, workbook: Workbook):
        """添加注释说明工作表"""
        comments_sheet = workbook.create_sheet(title="字段说明")

        field_descriptions = [
            ["字段名", "说明", "示例", "必填"],
            ["profile_name", "比特浏览器 profile 名称", "profile1", "是"],
            ["video_path", "视频文件绝对路径（MP4格式）", "C:/videos/sample.mp4", "是"],
            ["title", "视频标题（最大100字符）", "我的视频标题", "是"],
            ["description", "视频描述", "这是视频描述", "否"],
            ["tags", "标签（用|分隔，最多15个）", "教程|Python|自动化", "否"],
            ["visibility", "可见性设置", "public/unlisted/private/scheduled", "是"],
            ["schedule_time", "定时发布时间（visibility=scheduled时必填）", "2025-08-20 14:30:00", "条件"],
            ["audience_made_for_kids", "是否为儿童内容", "TRUE/FALSE", "是"],
            ["age_restriction_18plus", "是否18+限制", "TRUE/FALSE", "是"],
            ["playlist", "播放列表名称", "我的播放列表", "否"],
            ["allow_remix", "是否允许混剪", "TRUE/FALSE", "是"],
            ["language", "语言代码", "zh-CN/en-US", "是"],
            ["thumbnail_path", "缩略图路径", "C:/thumbnails/thumb.jpg", "否"],
            ["note", "备注信息", "备注", "否"]
        ]

        # 写入说明数据
        for row_num, row_data in enumerate(field_descriptions, 1):
            for col_num, value in enumerate(row_data, 1):
                cell = comments_sheet.cell(row=row_num, column=col_num, value=value)
                if row_num == 1:  # 表头
                    cell.font = HEADER_FONT
                    cell.fill = HEADER_FILL
                    cell.alignment = Alignment(horizontal='center', vertical='center')

        # 调整列宽
        comments_sheet.column_dimensions['A'].width = 25
        comments_sheet.column_dimensions['B'].width = 40
        comments_sheet.column_dimensions['C'].width = 30
        comments_sheet.column_dimensions['D'].width = 10
    
    def validate_tasks(self, tasks: List[TaskModel], check_files: bool = True) -> ValidationResult:
        """
        验证任务列表

        Args:
            tasks: 任务列表
            check_files: 是否检查文件存在性

        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult(is_valid=True)

        if not tasks:
            result.add_error("任务列表为空")
            return result

        # 统计信息
        profile_counts = {}
        total_size = 0

        for i, task in enumerate(tasks):
            task_prefix = f"任务 {i + 1} ({task.profile_name})"

            # 统计profile使用情况
            profile_counts[task.profile_name] = profile_counts.get(task.profile_name, 0) + 1

            # 文件检查
            if check_files:
                file_errors = task.validate_files_exist()
                for error in file_errors:
                    result.add_error(f"{task_prefix}: {error}")

                # 检查文件大小
                try:
                    if Path(task.video_path).exists():
                        file_size = Path(task.video_path).stat().st_size
                        total_size += file_size

                        # 检查文件大小（YouTube限制）
                        max_size = 128 * 1024 * 1024 * 1024  # 128GB
                        if file_size > max_size:
                            result.add_error(f"{task_prefix}: 视频文件过大 ({file_size / (1024**3):.1f}GB > 128GB)")
                        elif file_size < 1024:  # 1KB
                            result.add_warning(f"{task_prefix}: 视频文件过小 ({file_size} bytes)")
                except Exception as e:
                    result.add_warning(f"{task_prefix}: 无法检查文件大小 - {e}")

            # 内容检查
            self._validate_task_content(task, task_prefix, result)

        # 全局检查
        self._validate_global_constraints(tasks, profile_counts, total_size, result)

        return result

    def _validate_task_content(self, task: TaskModel, task_prefix: str, result: ValidationResult):
        """验证任务内容"""
        # 检查标题长度和内容
        if len(task.title) > 90:
            result.add_warning(f"{task_prefix}: 标题较长 ({len(task.title)} 字符)，建议控制在90字符以内")

        # 检查描述长度
        if task.description and len(task.description) > 4000:
            result.add_warning(f"{task_prefix}: 描述较长 ({len(task.description)} 字符)，建议控制在4000字符以内")

        # 检查标签
        if task.tags:
            tags = task.get_tags_list()
            if len(tags) > 10:
                result.add_warning(f"{task_prefix}: 标签数量较多 ({len(tags)} 个)，建议控制在10个以内")

            for tag in tags:
                if len(tag) > 25:
                    result.add_warning(f"{task_prefix}: 标签 '{tag}' 较长，建议控制在25字符以内")

        # 检查定时发布时间
        if task.is_scheduled():
            schedule_dt = task.get_schedule_datetime()
            if schedule_dt:
                from datetime import datetime, timedelta
                now = datetime.now()
                if schedule_dt < now + timedelta(minutes=5):
                    result.add_warning(f"{task_prefix}: 定时发布时间过近，建议至少提前5分钟")
                elif schedule_dt > now + timedelta(days=365):
                    result.add_warning(f"{task_prefix}: 定时发布时间过远，建议在一年以内")

    def _validate_global_constraints(self, tasks: List[TaskModel], profile_counts: Dict[str, int],
                                   total_size: int, result: ValidationResult):
        """验证全局约束"""
        # 检查profile任务分布
        for profile_name, count in profile_counts.items():
            if count > 10:
                result.add_warning(f"Profile '{profile_name}' 任务数量较多 ({count} 个)，建议分批执行")

        # 检查总文件大小
        if total_size > 10 * 1024 * 1024 * 1024:  # 10GB
            result.add_warning(f"总文件大小较大 ({total_size / (1024**3):.1f}GB)，请确保网络稳定")

        # 检查任务总数
        if len(tasks) > 50:
            result.add_warning(f"任务数量较多 ({len(tasks)} 个)，建议分批执行")

        # 检查重复标题
        titles = [task.title for task in tasks]
        duplicate_titles = [title for title in set(titles) if titles.count(title) > 1]
        if duplicate_titles:
            result.add_warning(f"发现重复标题: {', '.join(duplicate_titles[:3])}{'...' if len(duplicate_titles) > 3 else ''}")

    def get_tasks_statistics(self, tasks: List[TaskModel]) -> Dict[str, Any]:
        """获取任务统计信息"""
        if not tasks:
            return {"total_tasks": 0}

        stats = {
            "total_tasks": len(tasks),
            "profiles": {},
            "visibility_distribution": {},
            "language_distribution": {},
            "has_thumbnail": 0,
            "has_playlist": 0,
            "scheduled_tasks": 0,
            "kids_content": 0,
            "age_restricted": 0,
            "total_estimated_size": 0
        }

        for task in tasks:
            # Profile统计
            stats["profiles"][task.profile_name] = stats["profiles"].get(task.profile_name, 0) + 1

            # 可见性统计
            stats["visibility_distribution"][task.visibility] = stats["visibility_distribution"].get(task.visibility, 0) + 1

            # 语言统计
            stats["language_distribution"][task.language] = stats["language_distribution"].get(task.language, 0) + 1

            # 其他统计
            if task.thumbnail_path:
                stats["has_thumbnail"] += 1
            if task.playlist:
                stats["has_playlist"] += 1
            if task.is_scheduled():
                stats["scheduled_tasks"] += 1
            if task.audience_made_for_kids:
                stats["kids_content"] += 1
            if task.age_restriction_18plus:
                stats["age_restricted"] += 1

            # 文件大小估算
            try:
                if Path(task.video_path).exists():
                    stats["total_estimated_size"] += Path(task.video_path).stat().st_size
            except:
                pass

        return stats
